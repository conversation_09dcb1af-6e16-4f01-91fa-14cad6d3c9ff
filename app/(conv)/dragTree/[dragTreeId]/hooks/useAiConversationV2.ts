'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import type { UIMessage } from 'ai'
import { DefaultChatTransport } from 'ai'
import { useChat } from '@ai-sdk/react'
import { useTabStore } from '../stores/useTabStore'
import { ENABLE_CHAT_DEBUG_LOGGING } from '@/app/configs/feature-flags'
import type { ChatV2Request } from '@/app/api/aipane/chat-v2/types'

// Types
type ConversationData = {
  id: string
  title?: string
  createdAt: string
  updatedAt: string
  metadata?: any
}

type UseAiConversationV2Options = {
  // Existing conversation ID to load
  conversationId?: string
  // API endpoint for chat (should be /api/aipane/chat-v2)
  apiEndpoint: string
  // Model and settings
  model?: string
  context?: () => string
  // Context for new conversations
  contextIds?: string[]
  // Drag tree context for server upsert fallback
  dragTreeId?: string
  // Tab ID for title updates
  tabId?: string
  // Enable/disable the hook (useful for preventing race conditions)
  enabled?: boolean
}

type UseAiConversationV2Return = {
  // Conversation data
  conversation: ConversationData | null
  isLoadingConversation: boolean
  conversationError: string | null

  // Messages (AI SDK v5 UIMessage format with parts)
  messages: UIMessage[]

  // Chat functionality (from useChat)
  input: string
  handleInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void
  handleSubmit: (e: React.FormEvent) => void
  isLoading: boolean
  append: (message: { role: 'user' | 'assistant'; content: string }) => void
  sendMessage: (options: { text: string }) => void
  status: 'idle' | 'streaming' | 'error' | 'submitted' | 'ready'
  error: Error | null | undefined

  // Utilities
  refreshConversation: () => Promise<void>
}

export function useAiConversationV2(
  options: UseAiConversationV2Options
): UseAiConversationV2Return {
  const {
    conversationId: initialConversationId,
    apiEndpoint,
    model = 'gpt-4.1',
    context,
    contextIds,
    dragTreeId,
    tabId,
    enabled = true,
  } = options

  const { updateTabAiPaneData } = useTabStore()

  // Conversation state
  const [conversation, setConversation] = useState<ConversationData | null>(
    null
  )
  const [isLoadingConversation, setIsLoadingConversation] =
    useState<boolean>(false)
  const [conversationError, setConversationError] = useState<string | null>(
    null
  )

  // Refs for cleanup
  const isMountedRef = useRef(true)
  const abortControllerRef = useRef<AbortController | null>(null)

  // Messages state merged from persisted + streaming
  const [messages, setMessages] = useState<UIMessage[]>([])
  const [error, setError] = useState<Error | null>(null)
  const persistedMessagesRef = useRef<UIMessage[]>([])
  const isFirstMessageRef = useRef<boolean>(true)
  const [input, setInput] = useState<string>('')

  // AI SDK v5 chat transport to stream UIMessage with tool calls
  const chat = useChat({
    id: initialConversationId || 'pending',
    transport: new DefaultChatTransport({
      api: apiEndpoint,
      prepareSendMessagesRequest: ({ messages: outgoing }) => {
        const last = outgoing[outgoing.length - 1] as any
        let text = ''
        if (Array.isArray(last?.parts)) {
          const textPart = last.parts.find((p: any) => p?.type === 'text')
          text = textPart?.text || ''
        } else if (typeof last?.content === 'string') {
          text = last.content
        }

        if (!initialConversationId) {
          throw new Error('Conversation not ready')
        }

        const body: ChatV2Request = {
          message: text,
          conversationId: initialConversationId,
          model,
          contextIds,
          ...(dragTreeId && { dragTreeId }),
          ...(isFirstMessageRef.current && context && { context: context() }),
        }
        // Flip the first-message flag after composing payload
        isFirstMessageRef.current = false
        return { body }
      },
    }),
  })

  // Load existing messages from conversation
  useEffect(() => {
    if (!initialConversationId || !enabled) return

    const loadMessages = async () => {
      try {
        const url = new URL(
          `/api/aipane/conversations/${initialConversationId}`,
          window.location.origin
        )
        url.searchParams.set('limit', '50')
        url.searchParams.set('includeSteps', 'false')
        const response = await fetch(url.toString())
        if (response.ok) {
          const data = await response.json()
          const existingMessages = data.messages || []

          // Convert to UIMessage format and filter out system messages
          const uiMessages: UIMessage[] = existingMessages
            .filter((msg: any) => msg.role !== 'SYSTEM')
            .map((msg: any) => ({
              id: msg.id || `msg_${Date.now()}_${Math.random()}`,
              role: (msg.role || '').toLowerCase(),
              parts: [{ type: 'text', text: msg.content }],
            }))

          persistedMessagesRef.current = uiMessages
          isFirstMessageRef.current = uiMessages.length === 0
          setMessages([...uiMessages, ...chat.messages])

          // Ensure conversation object (with metadata) is set as soon as we fetch
          if (data.conversation) {
            setConversation(data.conversation)
            if (tabId && data.conversation?.id) {
              updateTabAiPaneData(tabId, {
                conversationId: data.conversation.id,
              })
            }
          }

          if (ENABLE_CHAT_DEBUG_LOGGING) {
            console.log(
              `📚 [useAiConversationV2] Loaded ${uiMessages.length} messages`
            )
          }
        } else if (response.status === 404) {
          // Conversation may not be created yet; treat as empty baseline
          persistedMessagesRef.current = []
          isFirstMessageRef.current = true
          setMessages([...chat.messages])
        }
      } catch (error) {
        console.error(
          '❌ [useAiConversationV2] Failed to load messages:',
          error
        )
      }
    }

    loadMessages()
  }, [initialConversationId, enabled])

  // Merge streaming updates with persisted baseline
  useEffect(() => {
    // Ensure correct ordering: oldest → newest.
    // persistedMessagesRef holds oldest→newest already. chat.messages also appends newest last.
    setMessages([...persistedMessagesRef.current, ...chat.messages])
  }, [chat.messages])

  // Load conversation data
  const refreshConversation = useCallback(async () => {
    if (!initialConversationId || !isMountedRef.current) {
      return
    }

    setIsLoadingConversation(true)
    setConversationError(null)

    try {
      // Abort any existing request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      abortControllerRef.current = new AbortController()

      const response = await fetch(
        `/api/aipane/conversations/${initialConversationId}`,
        {
          signal: abortControllerRef.current.signal,
        }
      )

      if (!response.ok) {
        throw new Error(`Failed to load conversation: ${response.status}`)
      }

      const data = await response.json()

      if (!isMountedRef.current) {
        return
      }

      setConversation(data.conversation)

      // Update tab title if available
      if (tabId && data.conversation?.title) {
        updateTabAiPaneData(tabId, {
          conversationId: data.conversation.id,
        })
      }
    } catch (error: any) {
      // Do not early-return on AbortError; always reach finally to clear loading
      if (error.name !== 'AbortError') {
        console.error(
          '❌ [useAiConversationV2] Failed to load conversation:',
          error
        )
        if (isMountedRef.current) {
          setConversationError(error.message || 'Failed to load conversation')
        }
      }
    } finally {
      if (isMountedRef.current) {
        setIsLoadingConversation(false)
      }
    }
  }, [initialConversationId, tabId, updateTabAiPaneData])

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value)
  }

  const sendMessage = useCallback(
    ({ text }: { text: string }) => {
      if (!enabled || !initialConversationId || !text.trim()) return
      try {
        chat.sendMessage({ text })
        setTimeout(() => {
          refreshConversation().catch(() => {})
        }, 200)
      } catch (e) {
        setError(e as Error)
      }
    },
    [chat, enabled, initialConversationId, refreshConversation]
  )

  const handleSubmit = (_e: React.FormEvent) => {
    // Parent component controls form submission; we provide a helper to send current input
    if (input.trim()) {
      sendMessage({ text: input })
      setInput('')
    }
  }

  const append = (message: { role: 'user' | 'assistant'; content: string }) => {
    sendMessage({ text: message.content })
  }

  // Load conversation on mount
  useEffect(() => {
    if (enabled) {
      refreshConversation().catch(() => {})
    } else {
      setIsLoadingConversation(false)
    }
  }, [enabled, refreshConversation])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [])

  return {
    // Conversation data
    conversation,
    isLoadingConversation,
    conversationError,

    // Messages (AI SDK v5 format with parts)
    messages,

    // Chat functionality
    input,
    handleInputChange,
    handleSubmit,
    isLoading: chat.status === 'streaming' || chat.status === 'submitted',
    append,
    sendMessage,
    status: chat.status as any,
    error,

    // Utilities
    refreshConversation,
  }
}
