'use client'

import { create } from 'zustand'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { type SupportedLanguageCode } from '@/app/constants/languages'

export const MAX_TOKENS = 20000

// Types for AI Pane functionality
export type AiPaneMode = 'overlay' | 'compress' | 'pin'
export type AiPaneType = 'generate' | 'chat'
export type AiModel =
  | 'gpt-4.1'
  | 'gpt-4.1-mini'
  | 'openai-o3'
  | 'claude-4-sonnet'
  | 'gemini-2.5-pro'

export type ContextItem = {
  id: string
  title: string
  type: 'category' | 'question'
  parentId?: string
  categoryId?: string
  content?: string
  selected: boolean
}

export type AiPaneSettings = {
  type: AiPaneType
  model: AiModel
  context: ContextItem[]
  prompt: string
  promptTemplate?: string // Track selected template name for smart title generation
  files: File[]
  images: File[]
  language?: SupportedLanguageCode // Language override for this generation only
}

type AiPaneState = {
  // Panel state
  isOpen: boolean
  mode: AiPaneMode
  width: number
  minWidth: number
  maxWidth: number

  // Settings
  settings: AiPaneSettings
  contextTokenCount: number

  // Context loading state - track which items are being fetched
  loadingContextIds: Set<string>

  // UI state
  isLoading: boolean

  // Actions
  setIsOpen: (isOpen: boolean) => void
  setMode: (mode: AiPaneMode) => void
  setWidth: (width: number) => void
  setContextTokenCount: (count: number) => void

  // Settings actions
  setType: (type: AiPaneType) => void
  setModel: (model: AiModel) => void
  setContext: (context: ContextItem[]) => void
  toggleContextItem: (id: string) => void
  toggleCategoryWithChildren: (
    categoryId: string,
    childrenIds: string[]
  ) => void
  selectAllContext: () => void
  clearAllContext: () => void
  setPrompt: (prompt: string) => void
  setPromptTemplate: (template?: string) => void
  setLanguage: (language?: SupportedLanguageCode) => void
  addFile: (file: File) => void
  removeFile: (index: number) => void
  addImage: (image: File) => void
  removeImage: (index: number) => void

  // Context loading actions
  setContextLoading: (id: string, loading: boolean) => void
  isContextLoading: (id: string) => boolean
  isAllSelectedContentLoaded: () => boolean

  // Utility actions
  resetSettings: () => void
}

const defaultSettings: AiPaneSettings = {
  type: 'generate',
  model: 'gpt-4.1',
  context: [],
  prompt: '',
  promptTemplate: undefined,
  files: [],
  images: [],
}

export const useAiPaneStore = create<AiPaneState>((set, get) => ({
  // Initial state
  isOpen: false,
  mode: 'compress',
  width: 400,
  minWidth: 300,
  maxWidth: 800,
  contextTokenCount: 0,
  loadingContextIds: new Set<string>(),

  settings: defaultSettings,
  isLoading: false,

  // Panel actions
  setIsOpen: (isOpen: boolean) => set({ isOpen }),
  setMode: (mode: AiPaneMode) => set({ mode }),
  setWidth: (width: number) => {
    const { minWidth, maxWidth } = get()
    const clampedWidth = Math.max(minWidth, Math.min(maxWidth, width))
    set({ width: clampedWidth })
  },
  setContextTokenCount: (count: number) => set({ contextTokenCount: count }),

  // Settings actions
  setType: (type: AiPaneType) =>
    set(state => ({
      settings: { ...state.settings, type, prompt: '' },
    })),

  setModel: (model: AiModel) =>
    set(state => ({
      settings: { ...state.settings, model },
    })),

  setContext: (context: ContextItem[]) =>
    set(state => ({
      settings: { ...state.settings, context },
    })),

  toggleContextItem: (id: string) =>
    set(state => ({
      settings: {
        ...state.settings,
        context: state.settings.context.map(item =>
          item.id === id ? { ...item, selected: !item.selected } : item
        ),
      },
    })),

  toggleCategoryWithChildren: (categoryId: string, childrenIds: string[]) =>
    set(state => {
      // Find the category and determine its new selected state
      const category = state.settings.context.find(
        item => item.id === categoryId
      )
      if (!category) return state

      const newSelectedState = !category.selected

      // Update category and all its children in a single operation
      return {
        settings: {
          ...state.settings,
          context: state.settings.context.map(item => {
            // Toggle the category itself
            if (item.id === categoryId) {
              return { ...item, selected: newSelectedState }
            }
            // Toggle all children to match the category
            if (childrenIds.includes(item.id)) {
              return { ...item, selected: newSelectedState }
            }
            // Leave other items unchanged
            return item
          }),
        },
      }
    }),

  selectAllContext: () =>
    set(state => ({
      settings: {
        ...state.settings,
        context: state.settings.context.map(item => ({
          ...item,
          selected: true,
        })),
      },
    })),

  clearAllContext: () =>
    set(state => ({
      settings: {
        ...state.settings,
        context: state.settings.context.map(item => ({
          ...item,
          selected: false,
        })),
      },
    })),

  setPrompt: (prompt: string) =>
    set(state => ({
      settings: { ...state.settings, prompt },
    })),

  setPromptTemplate: (promptTemplate?: string) =>
    set(state => ({
      settings: { ...state.settings, promptTemplate },
    })),

  setLanguage: (language?: SupportedLanguageCode) =>
    set(state => ({
      settings: { ...state.settings, language },
    })),

  addFile: (file: File) =>
    set(state => ({
      settings: {
        ...state.settings,
        files: [...state.settings.files, file],
      },
    })),

  removeFile: (index: number) =>
    set(state => ({
      settings: {
        ...state.settings,
        files: state.settings.files.filter((_, i) => i !== index),
      },
    })),

  addImage: (image: File) =>
    set(state => ({
      settings: {
        ...state.settings,
        images: [...state.settings.images, image],
      },
    })),

  removeImage: (index: number) =>
    set(state => ({
      settings: {
        ...state.settings,
        images: state.settings.images.filter((_, i) => i !== index),
      },
    })),

  // Context loading actions
  setContextLoading: (id: string, loading: boolean) => {
    const { loadingContextIds } = get()
    const newLoadingIds = new Set(loadingContextIds)
    if (loading) {
      newLoadingIds.add(id)
    } else {
      newLoadingIds.delete(id)
    }
    set({ loadingContextIds: newLoadingIds })
  },

  isContextLoading: (id: string) => {
    const { loadingContextIds } = get()
    return loadingContextIds.has(id)
  },

  isAllSelectedContentLoaded: () => {
    const { settings, loadingContextIds } = get()
    const selectedIds = settings.context
      .filter(item => item.selected)
      .map(item => item.id)

    // If no items are selected, consider it loaded
    if (selectedIds.length === 0) {
      return true
    }

    // Check if any selected items are currently loading
    const hasLoadingItems = selectedIds.some(id => loadingContextIds.has(id))
    if (hasLoadingItems) {
      return false
    }

    // Also verify that content actually exists for all selected items
    const nodeContent = useDragTreeStore.getState().nodeContent
    const hasAllContent = selectedIds.every(id => {
      if (!nodeContent.has(id)) return false
      const contentMap = nodeContent.get(id)!
      if (contentMap.size === 0) return false

      return Array.from(contentMap.values()).some(item => {
        // Check if the content item has actual text content
        const hasContentText = !!(item as any)?.contentText
        const hasMetadata = !!(item as any)?.metadata
        return hasContentText || hasMetadata
      })
    })

    return hasAllContent
  },

  // Utility actions
  resetSettings: () => set({ settings: defaultSettings }),
}))
