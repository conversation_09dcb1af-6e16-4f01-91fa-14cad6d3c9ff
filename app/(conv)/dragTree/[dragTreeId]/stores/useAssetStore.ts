'use client'

import toast from 'react-hot-toast'
import { create } from 'zustand'
import { JSONContent } from '@tiptap/core'
import type {
  AIGenerationMeta,
  AIGenerationContent,
} from '@/app/types/ai-generation'

export type Asset = {
  id: string
  title: string
  content: string
  /**
   * For chat assets, store individual messages instead of one big content
   * block so the UI can render them properly. Not used for generate assets.
   */
  messages?: { role: 'user' | 'assistant'; content: string }[]
  tiptapContent?: JSONContent
  type: string // Extensible asset type
  model: string
  prompt: string
  contextIds: string[]
  createdAt: Date
  updatedAt: Date
  viewed: boolean
  dragTreeId: string
  // Database integration fields
  persistedInDb?: boolean
  isContentLoaded?: boolean
  generationInput?: string
  generationOutput?: string
  version?: number // For optimistic locking
  config?: any
  metadata?: any
}

type AssetStore = {
  assets: Asset[]
  isAssetSidebarOpen: boolean
  hasNewAssets: boolean

  // Asset management
  addAsset: (
    asset: Partial<Asset> &
      Pick<
        Asset,
        | 'title'
        | 'content'
        | 'type'
        | 'model'
        | 'prompt'
        | 'contextIds'
        | 'dragTreeId'
      >
  ) => string
  updateAsset: (id: string, updates: Partial<Asset>) => void
  removeAsset: (id: string) => void
  deleteAsset: (id: string) => Promise<{ success: boolean; error?: string }>
  markAssetAsViewed: (id: string) => void
  markAllAssetsAsViewed: () => void
  getAssetsByDragTree: (dragTreeId: string) => Asset[]

  // Database integration methods
  bulkLoadAssets: (
    generationMetas: AIGenerationMeta[],
    dragTreeId?: string
  ) => void
  setAssetContent: (id: string, content: AIGenerationContent) => void
  updateAssetContent: (
    id: string,
    content: string,
    tiptapContent?: JSONContent
  ) => Promise<void>
  isAssetContentLoaded: (id: string) => boolean

  // Sidebar management
  openAssetSidebar: () => void
  closeAssetSidebar: () => void
  toggleAssetSidebar: () => void

  // Notification management
  clearNewAssetNotification: () => void
}

// Utility function to convert AI generation metadata to Asset
const convertAIGenerationToAsset = (
  generation: AIGenerationMeta,
  dragTreeId: string
): Asset => {
  const modelName = generation.config?.model_name || 'Unknown'

  const isRead = generation.metadata?.is_read === true
  return {
    id: generation.id,
    title: generation.title || 'AI Generation',
    content: '', // Content will be loaded lazily
    type: 'generate' as const,
    model: modelName,
    prompt: '', // Will be loaded with content
    contextIds: [], // Will be loaded with content
    createdAt: generation.createdAt,
    updatedAt: generation.updatedAt,
    viewed: isRead,
    dragTreeId: dragTreeId,
    persistedInDb: true,
    isContentLoaded: false,
    version: generation.version, // Include version for optimistic locking
    config: generation.config,
    metadata: generation.metadata,
  }
}

export const useAssetStore = create<AssetStore>((set, get) => ({
  assets: [], // Start with empty assets - will be loaded from database
  isAssetSidebarOpen: false,
  hasNewAssets: false, // Start without notifications

  addAsset: assetData => {
    // Use provided ID if it exists (from database), otherwise generate one for local assets
    const id =
      assetData.id ||
      `asset-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    const newAsset: Asset = {
      ...assetData,
      id,
      createdAt: assetData.createdAt || new Date(),
      updatedAt: assetData.updatedAt || new Date(),
      viewed: assetData.viewed !== undefined ? assetData.viewed : false,
      persistedInDb: assetData.persistedInDb || false,
      isContentLoaded:
        assetData.isContentLoaded !== undefined
          ? assetData.isContentLoaded
          : true,
    }

    set(state => ({
      assets: [newAsset, ...state.assets], // Add to beginning
      hasNewAssets: true, // Set notification
    }))

    return id
  },

  updateAsset: (id, updates) => {
    set(state => ({
      assets: state.assets.map(asset =>
        asset.id === id ? { ...asset, ...updates } : asset
      ),
    }))
  },

  removeAsset: id => {
    set(state => ({
      assets: state.assets.filter(asset => asset.id !== id),
    }))
  },

  deleteAsset: async (id: string) => {
    const { assets } = get()
    const asset = assets.find(a => a.id === id)

    if (!asset) {
      console.warn(`Asset ${id} not found for deletion`)
      return { success: false, error: 'Asset not found' }
    }

    // Optimistically remove from local store
    set(state => ({
      assets: state.assets.filter(asset => asset.id !== id),
    }))

    // If asset is persisted in DB, mark as deleted
    if (asset.persistedInDb) {
      try {
        if (asset.type === 'generate') {
          // Handle AI generation deletion
          if (asset.version === undefined) {
            console.warn(
              'Generate asset missing version, skipping database deletion'
            )
            return { success: true }
          }

          const { markAIGenerationDeleted } = await import(
            '@/app/server-actions/drag-tree/update_ai_generation'
          )

          const result = await markAIGenerationDeleted(id, asset.version)

          if (!result.success) {
            // Rollback optimistic update on failure
            set(state => ({
              assets: [asset, ...state.assets],
            }))
            console.error(
              'Failed to delete generation in database:',
              result.error
            )
            return { success: false, error: result.error }
          }
        } else if (asset.type === 'chat') {
          // Handle AI conversation deletion
          const { markAIConversationDeleted } = await import(
            '@/app/server-actions/drag-tree/update_ai_generation'
          )

          const result = await markAIConversationDeleted(id)

          if (!result.success) {
            // Rollback optimistic update on failure
            set(state => ({
              assets: [asset, ...state.assets],
            }))
            console.error(
              'Failed to delete conversation in database:',
              result.error
            )
            return { success: false, error: result.error }
          }
        } else {
          console.warn(`Unknown asset type for deletion: ${asset.type}`)
          // For unknown types, just remove locally
        }

        console.log(
          `✅ [Asset Store] Successfully deleted ${asset.type} asset ${id}`
        )
        return { success: true }
      } catch (error) {
        // Rollback optimistic update on error
        set(state => ({
          assets: [asset, ...state.assets],
        }))
        console.error('Error deleting asset:', error)
        return { success: false, error: 'Failed to delete asset' }
      }
    }

    // For non-persisted assets, just local removal is sufficient
    console.log(`✅ [Asset Store] Removed non-persisted asset ${id}`)
    return { success: true }
  },

  markAssetAsViewed: id => {
    const { assets } = get()
    const asset = assets.find(a => a.id === id)

    if (!asset) return

    // Optimistically update local state first
    set(state => ({
      assets: state.assets.map(a => (a.id === id ? { ...a, viewed: true } : a)),
    }))

    // Persist read state to database if necessary
    if (
      asset.persistedInDb &&
      asset.metadata?.is_read !== true &&
      asset.version !== undefined
    ) {
      import('@/app/server-actions/drag-tree/update_ai_generation').then(
        async mod => {
          try {
            const result = await mod.markAIGenerationRead(id, asset.version!)
            if (result.success && result.newVersion) {
              // Update local version to keep in sync
              set(state => ({
                assets: state.assets.map(a =>
                  a.id === id ? { ...a, version: result.newVersion } : a
                ),
              }))
            }
          } catch (e) {
            console.error('Failed to mark generation read:', e)
          }
        }
      )
    }
  },

  markAllAssetsAsViewed: () => {
    set(state => ({
      assets: state.assets.map(asset => ({ ...asset, viewed: true })),
      hasNewAssets: false,
    }))
  },

  getAssetsByDragTree: dragTreeId => {
    return get().assets.filter(asset => asset.dragTreeId === dragTreeId)
  },

  // Database integration methods
  bulkLoadAssets: (
    generationMetas: AIGenerationMeta[],
    dragTreeId?: string
  ) => {
    set(state => {
      // Use provided dragTreeId or try to infer from existing assets
      const targetDragTreeId =
        dragTreeId || state.assets[0]?.dragTreeId || 'unknown'

      // Convert database records to assets
      const newAssets = generationMetas.map(meta =>
        convertAIGenerationToAsset(meta, targetDragTreeId)
      )

      // Safe merging with timestamp comparison to prevent overwriting recent local changes
      const existingAssetsMap = new Map(
        state.assets.map(asset => [asset.id, asset])
      )
      const mergedAssets: Asset[] = []
      const assetsToAdd: Asset[] = []

      // Process new assets from server
      newAssets.forEach(newAsset => {
        const existingAsset = existingAssetsMap.get(newAsset.id)

        if (!existingAsset) {
          // New asset from server - add it
          assetsToAdd.push(newAsset)
        } else {
          // Asset exists locally - compare timestamps for safe merging
          const serverUpdatedAt = newAsset.createdAt || new Date(0) // Use createdAt as fallback
          const localUpdatedAt = existingAsset.createdAt || new Date(0)

          // Only replace if server data is newer OR if local asset has no content loaded
          if (
            serverUpdatedAt > localUpdatedAt ||
            !existingAsset.isContentLoaded
          ) {
            // Server has newer data - merge but preserve local UI state
            mergedAssets.push({
              ...newAsset,
              // Preserve local UI state that shouldn't be overwritten
              viewed: existingAsset.viewed, // Keep local viewed status
              tiptapContent:
                existingAsset.tiptapContent || newAsset.tiptapContent, // Preserve local edits
              // If local asset has content loaded and was modified, prefer local content
              ...(existingAsset.isContentLoaded &&
                existingAsset.metadata?.lastEditedAt && {
                  content: existingAsset.content,
                  metadata: {
                    ...newAsset.metadata,
                    ...existingAsset.metadata, // Preserve local metadata
                  },
                }),
            })
          } else {
            // Local data is newer or equal - keep existing asset
            mergedAssets.push(existingAsset)
          }
        }
      })

      // Combine existing assets (that weren't replaced) with new/merged assets
      const processedAssetIds = new Set([...newAssets.map(a => a.id)])

      const unprocessedExistingAssets = state.assets.filter(
        asset => !processedAssetIds.has(asset.id)
      )

      // Combine and sort all assets by time (latest first)
      const allAssets = [
        ...unprocessedExistingAssets,
        ...mergedAssets,
        ...assetsToAdd,
      ]
      const sortedAssets = allAssets.sort((a, b) => {
        // Sort by updatedAt first (for recently modified assets), then by createdAt
        const aTime = a.updatedAt || a.createdAt
        const bTime = b.updatedAt || b.createdAt
        return new Date(bTime).getTime() - new Date(aTime).getTime()
      })

      return {
        assets: sortedAssets,
        hasNewAssets:
          state.hasNewAssets || assetsToAdd.some(asset => !asset.viewed),
      }
    })
  },

  setAssetContent: (id: string, content: AIGenerationContent) => {
    set(state => ({
      assets: state.assets.map(asset =>
        asset.id === id
          ? {
              ...asset,
              content: content.generation_output, // Use generation_output as primary content
              prompt: content.generation_input,
              generationInput: content.generation_input,
              generationOutput: content.generation_output,
              isContentLoaded: true,
              title: content.title || asset.title,
              version: content.version, // Include version for optimistic locking
              config: content.config,
              metadata: content.metadata,
            }
          : asset
      ),
    }))
  },

  updateAssetContent: async (
    id: string,
    content: string,
    tiptapContent?: JSONContent
  ) => {
    const { assets } = get()
    const asset = assets.find(a => a.id === id)

    if (!asset || !asset.persistedInDb || asset.version === undefined) {
      // For non-persisted assets or assets without version, just update locally
      set(state => ({
        assets: state.assets.map(a =>
          a.id === id
            ? {
                ...a,
                content,
                tiptapContent,
                metadata: {
                  ...a.metadata,
                  lastEditedAt: new Date().toISOString(),
                  editedFromTiptap: !!tiptapContent,
                },
              }
            : a
        ),
      }))
      return
    }

    // Store the original asset state for rollback
    const originalAsset = { ...asset }

    // Optimistically update the UI immediately
    set(state => ({
      assets: state.assets.map(a =>
        a.id === id
          ? {
              ...a,
              content,
              tiptapContent,
              metadata: {
                ...a.metadata,
                lastEditedAt: new Date().toISOString(),
                editedFromTiptap: !!tiptapContent,
                optimisticUpdate: true, // Mark as optimistic
              },
            }
          : a
      ),
    }))

    try {
      // Import the server action dynamically to avoid circular dependencies
      const { updateAIGeneration } = await import(
        '@/app/server-actions/drag-tree/update_ai_generation'
      )

      // Call server with optimistic locking
      const result = await updateAIGeneration(
        id,
        {
          content: JSON.stringify(tiptapContent), // Tiptap JSON is the source of truth
          metadata: { editedFromTiptap: true },
        },
        asset.version
      )

      if (result.success && result.newVersion) {
        // Success - update with new version and remove optimistic flag
        set(state => ({
          assets: state.assets.map(a =>
            a.id === id
              ? {
                  ...a,
                  version: result.newVersion,
                  metadata: {
                    ...a.metadata,
                    optimisticUpdate: false,
                    lastSyncedAt: new Date().toISOString(),
                  },
                }
              : a
          ),
        }))
      } else {
        // Server error - rollback and show error
        console.error('❌ [Asset Store] Server update failed:', result.error)

        // Determine error message
        const isConflict = result.error?.includes('Version conflict')
        const errorMessage = isConflict
          ? 'Heads up! This was edited in another tab. Your latest changes were not saved.'
          : 'Could not save changes.'

        set(state => ({
          assets: state.assets.map(a =>
            a.id === id
              ? {
                  ...originalAsset,
                  metadata: {
                    ...originalAsset.metadata,
                    lastError: errorMessage,
                    lastErrorAt: new Date().toISOString(),
                  },
                }
              : a
          ),
        }))

        toast.error(errorMessage)
      }
    } catch (error) {
      console.error('❌ [Asset Store] Network error during update:', error)

      // Rollback to original state
      const errorMessage = 'Network error: Changes not saved.'
      toast.error(errorMessage)

      set(state => ({
        assets: state.assets.map(a =>
          a.id === id
            ? {
                ...originalAsset,
                metadata: {
                  ...originalAsset.metadata,
                  lastError: errorMessage,
                  lastErrorAt: new Date().toISOString(),
                },
              }
            : a
        ),
      }))
    }
  },

  isAssetContentLoaded: (id: string) => {
    const asset = get().assets.find(a => a.id === id)
    return asset?.isContentLoaded || false
  },

  openAssetSidebar: () => {
    set({ isAssetSidebarOpen: true })
  },

  closeAssetSidebar: () => {
    set({ isAssetSidebarOpen: false })
  },

  toggleAssetSidebar: () => {
    set(state => ({ isAssetSidebarOpen: !state.isAssetSidebarOpen }))
  },

  clearNewAssetNotification: () => {
    set({ hasNewAssets: false })
  },
}))

// Helper function to get unviewed asset count
export const getUnviewedAssetCount = (dragTreeId: string): number => {
  const assets = useAssetStore.getState().getAssetsByDragTree(dragTreeId)
  return assets.filter(asset => !asset.viewed).length
}
