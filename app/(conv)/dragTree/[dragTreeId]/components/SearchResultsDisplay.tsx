/**
 * SearchResultsDisplay Component
 * Compact citation system with global popup showing all sources
 * Optimized for space efficiency and no container clipping
 */

import React, { useState, useEffect } from 'react'
import { ExternalLink, Search, Loader2, X } from 'lucide-react'
import { createPortal } from 'react-dom'

export type SearchMetadata = {
  keyword: string
  url: string
  icon?: string
  snippets: string[]
  timestamp: string
  source: 'brave_search'
  // Enhanced metadata for better UX
  domain?: string // Extracted domain name
  page_age?: string // Age of the web page
  language?: string // Language classification
  site_description?: string // Clean description without snippets
}

export type SearchResultsDisplayProps = {
  searchResults?: SearchMetadata[]
  className?: string
}

export type SearchProgressIndicatorProps = {
  isSearching: boolean
  currentQuery?: string
  className?: string
}

export type SourceCitationsProps = {
  searchResults?: SearchMetadata[]
  maxSources?: number
  className?: string
}

export type SourceMenuProps = {
  sources: SearchMetadata[]
  isOpen: boolean
  onClose: () => void
  position?: { x: number; y: number }
  highlightedIndex?: number
}

/**
 * Search Progress Indicator - shows when AI is actively searching
 */
export function SearchProgressIndicator({
  isSearching,
  currentQuery,
  className = '',
}: SearchProgressIndicatorProps) {
  if (!isSearching) return null

  return (
    <div
      className={`flex items-center gap-2 text-sm text-blue-600 ${className}`}
    >
      <Loader2 className="h-4 w-4 animate-spin" />
      <span>Searching{currentQuery ? ` "${currentQuery}"` : ''}...</span>
    </div>
  )
}

/**
 * Global Source Menu - shows all search results with highlighting
 */
function SourceMenu({
  sources,
  isOpen,
  onClose,
  position,
  highlightedIndex = 0,
}: SourceMenuProps) {
  if (!isOpen) return null

  const menuContent = (
    <>
      {/* Backdrop */}
      <div className="fixed inset-0 z-[9998] bg-black/20" onClick={onClose} />

      {/* Menu */}
      <div
        className="fixed z-[9999] w-[600px] bg-white rounded-lg shadow-xl border border-gray-200 max-h-[70vh] overflow-hidden"
        style={{
          left: position?.x || 0,
          top: position?.y || 0,
          transform: 'translate(-50%, -50%)',
        }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-100 bg-gray-50">
          <div className="flex items-center gap-2">
            <Search className="h-4 w-4 text-gray-600" />
            <span className="text-sm font-medium text-gray-900">
              Search Sources ({sources.length})
            </span>
          </div>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-200 rounded-full"
          >
            <X className="h-3 w-3 text-gray-500" />
          </button>
        </div>

        {/* Sources List - Enhanced with query grouping */}
        <div className="max-h-[calc(70vh-120px)] overflow-y-auto">
          {(() => {
            // Group sources by keyword for better organization
            const groupedSources = sources.reduce(
              (acc, source) => {
                if (!acc[source.keyword]) {
                  acc[source.keyword] = []
                }
                acc[source.keyword].push(source)
                return acc
              },
              {} as Record<string, typeof sources>
            )

            return Object.entries(groupedSources).map(
              ([keyword, keywordSources]) => (
                <div
                  key={keyword}
                  className="border-b border-gray-100 last:border-b-0"
                >
                  {/* Query header - Enhanced visual hierarchy */}
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-4 py-4 border-b-2 border-blue-200">
                    <div className="flex items-center gap-3">
                      <div className="p-1.5 bg-blue-100 rounded-md">
                        <Search className="h-4 w-4 text-blue-700" />
                      </div>
                      <div>
                        <span className="text-base font-semibold text-blue-900 block">
                          "&ldquo;{keyword}&rdquo;"
                        </span>
                        <span className="text-xs text-blue-600 font-medium">
                          {keywordSources.length} result
                          {keywordSources.length !== 1 ? 's' : ''}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Results for this query - Enhanced indentation and styling */}
                  <div className="bg-white">
                    {keywordSources.map((source, _index) => {
                      const globalIndex = sources.indexOf(source)
                      return (
                        <div
                          key={source.url}
                          className={`ml-6 pl-4 pr-4 py-3 border-l-2 border-l-gray-200 border-b border-gray-50 last:border-b-0 transition-all duration-200 ${
                            globalIndex === highlightedIndex
                              ? 'bg-blue-50 border-l-blue-400 border-blue-200'
                              : 'hover:bg-gray-50 hover:border-l-gray-300'
                          }`}
                        >
                          {/* Source Header */}
                          <div className="flex items-start gap-3 mb-2">
                            <div className="flex items-center gap-3 min-w-0 flex-1">
                              {/* Favicon */}
                              {/* eslint-disable-next-line @next/next/no-img-element */}
                              {source.icon ? (
                                <img
                                  src={source.icon}
                                  alt="Source favicon"
                                  className="h-5 w-5 rounded-sm flex-shrink-0 object-cover"
                                  onError={e => {
                                    const target =
                                      e.currentTarget as HTMLImageElement
                                    target.style.display = 'none'
                                  }}
                                />
                              ) : (
                                <ExternalLink className="h-5 w-5 text-gray-400 flex-shrink-0" />
                              )}

                              {/* Domain and URL */}
                              <div className="flex-1 min-w-0">
                                <span className="text-sm font-medium text-gray-900 block">
                                  {source.domain ||
                                    getDomainFromUrl(source.url)}
                                </span>
                                <span className="text-xs text-gray-500 truncate block">
                                  {source.url}
                                </span>
                              </div>
                            </div>

                            {/* Visit Link */}
                            <a
                              href={source.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800 font-medium px-3 py-1.5 rounded hover:bg-blue-50 transition-colors flex-shrink-0"
                              onClick={e => e.stopPropagation()}
                            >
                              Visit
                              <ExternalLink className="h-3 w-3" />
                            </a>
                          </div>

                          {/* Enhanced description */}
                          {source.site_description && (
                            <p className="text-xs text-gray-700 line-clamp-2 ml-8">
                              {source.site_description}
                            </p>
                          )}
                        </div>
                      )
                    })}
                  </div>
                </div>
              )
            )
          })()}
        </div>
      </div>
    </>
  )

  // Render as portal to avoid clipping issues
  return typeof window !== 'undefined'
    ? createPortal(menuContent, document.body)
    : null
}

/**
 * Source Citations - compact inline citations with clickable icons
 */
export function SourceCitations({
  searchResults,
  maxSources = 5,
  className = '',
}: SourceCitationsProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [menuPosition, setMenuPosition] = useState<{ x: number; y: number }>({
    x: 0,
    y: 0,
  })
  const [highlightedIndex, setHighlightedIndex] = useState(0)

  // Close on Escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') setIsMenuOpen(false)
    }
    if (isMenuOpen) {
      document.addEventListener('keydown', handleEscape)
      return () => document.removeEventListener('keydown', handleEscape)
    }
  }, [isMenuOpen])

  if (!searchResults || searchResults.length === 0) return null

  // Unique sources by URL (deduplicated)
  const uniqueSources = searchResults.reduce((acc, source) => {
    if (!acc.find(s => s.url === source.url)) {
      acc.push(source)
    }
    return acc
  }, [] as SearchMetadata[])

  // For display icons, deduplicate by favicon to prevent showing same icons
  const uniqueIconSources = uniqueSources.reduce((acc, source) => {
    const iconKey = source.icon || getDomainFromUrl(source.url)
    if (!acc.find(s => (s.icon || getDomainFromUrl(s.url)) === iconKey)) {
      acc.push(source)
    }
    return acc
  }, [] as SearchMetadata[])

  const MAX_ICONS = 3
  const displaySources = uniqueIconSources.slice(
    0,
    Math.min(MAX_ICONS, maxSources)
  )
  const extraCount = uniqueSources.length - displaySources.length

  const handleOpenPopup = (_event: React.MouseEvent) => {
    // Center popup
    setMenuPosition({ x: window.innerWidth / 2, y: window.innerHeight / 2 })
    setHighlightedIndex(0)
    setIsMenuOpen(true)
  }

  return (
    <>
      <button
        onClick={handleOpenPopup}
        className={`flex items-center gap-2 ${className}`}
        title={`View ${uniqueSources.length} sources`}
      >
        {/* Icon stack */}
        <div className="flex -space-x-2">
          {displaySources.map((source, index) => {
            const key = source.url
            const sizeClasses = 'w-6 h-6'
            return source.icon ? (
              <img
                key={key}
                src={source.icon}
                alt={getDomainFromUrl(source.url)}
                className={`${sizeClasses} rounded-full border border-white shadow object-cover bg-white`}
                onError={e => {
                  // fallback to empty avatar if favicon fails
                  e.currentTarget.style.display = 'none'
                }}
              />
            ) : (
              <div
                key={key}
                className={`${sizeClasses} rounded-full bg-blue-100 border border-blue-200 flex items-center justify-center text-[10px] font-medium text-blue-700`}
              >
                {index + 1}
              </div>
            )
          })}

          {/* Extra count badge */}
          {extraCount > 0 && (
            <div className="w-6 h-6 rounded-full bg-blue-100 border border-blue-200 flex items-center justify-center text-[10px] font-medium text-blue-700">
              +{extraCount}
            </div>
          )}
        </div>
      </button>

      {/* Popup */}
      <SourceMenu
        sources={uniqueSources.slice(0, maxSources)}
        isOpen={isMenuOpen}
        onClose={() => setIsMenuOpen(false)}
        position={menuPosition}
        highlightedIndex={highlightedIndex}
      />
    </>
  )
}

/**
 * Legacy SearchResultsDisplay - kept for backward compatibility
 */
export function SearchResultsDisplay({
  searchResults,
  className = '',
}: SearchResultsDisplayProps) {
  if (!searchResults || searchResults.length === 0) {
    return null
  }

  // Group search results by keyword to show related searches together
  const groupedResults = searchResults.reduce(
    (acc, result) => {
      if (!acc[result.keyword]) {
        acc[result.keyword] = []
      }
      acc[result.keyword].push(result)
      return acc
    },
    {} as Record<string, SearchMetadata[]>
  )

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
        <Search className="h-4 w-4" />
        <span>Web Search Results</span>
        <span className="text-xs text-gray-500">
          ({searchResults.length} source{searchResults.length !== 1 ? 's' : ''})
        </span>
      </div>

      <div className="space-y-3">
        {Object.entries(groupedResults).map(([keyword, results]) => (
          <div
            key={keyword}
            className="border border-gray-200 dark:border-gray-700 rounded-lg p-3 bg-gray-50 dark:bg-gray-800/50"
          >
            <div className="flex items-center gap-2 mb-3">
              <div className="text-sm font-medium text-gray-900 dark:text-gray-100 bg-blue-100 dark:bg-blue-900/30 px-2 py-1 rounded">
                🔍 &quot;{keyword}&quot; ({results.length} results)
              </div>
            </div>

            <div className="space-y-2">
              {results.map((result, index) => (
                <div key={`${result.url}-${index}`} className="group">
                  <a
                    href={result.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-start gap-3 p-2 rounded border border-transparent hover:border-gray-300 dark:hover:border-gray-600 hover:bg-white dark:hover:bg-gray-900/50 transition-colors"
                  >
                    {/* Favicon or default icon */}
                    <div className="flex-shrink-0 mt-0.5">
                      {/* eslint-disable-next-line @next/next/no-img-element */}
                      {result.icon ? (
                        <img
                          src={result.icon}
                          alt={`${getDomainFromUrl(result.url)} favicon`}
                          className="h-4 w-4 rounded object-cover"
                          onError={e => {
                            // Fallback to default icon if favicon fails to load
                            const target = e.currentTarget as HTMLImageElement
                            target.style.display = 'none'
                          }}
                        />
                      ) : (
                        <ExternalLink className="h-4 w-4 text-gray-400" />
                      )}
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="text-sm text-blue-600 dark:text-blue-400 group-hover:text-blue-800 dark:group-hover:text-blue-300 font-medium truncate">
                        {result.domain || getDomainFromUrl(result.url)}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400 truncate mt-0.5">
                        {result.url}
                      </div>

                      {/* Enhanced description */}
                      {result.site_description && (
                        <p className="text-xs text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                          {result.site_description}
                        </p>
                      )}
                    </div>

                    {/* External link indicator */}
                    <ExternalLink className="h-3 w-3 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0 mt-1" />
                  </a>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

/**
 * Extract domain from URL for display
 */
function getDomainFromUrl(url: string): string {
  try {
    const domain = new URL(url).hostname
    return domain.startsWith('www.') ? domain.slice(4) : domain
  } catch {
    return url
  }
}

/**
 * Type guard to check if content metadata contains search results
 */
export function hasSearchResults(
  contentMetadata: any
): contentMetadata is { searchResults: SearchMetadata[] } {
  return (
    contentMetadata &&
    typeof contentMetadata === 'object' &&
    Array.isArray(contentMetadata.searchResults) &&
    contentMetadata.searchResults.length > 0 &&
    // Validate that each search result has required properties
    contentMetadata.searchResults.every(
      (result: any) =>
        result &&
        typeof result === 'object' &&
        typeof result.url === 'string' &&
        typeof result.keyword === 'string' &&
        Array.isArray(result.snippets)
    )
  )
}

/**
 * Type guard to validate a single search result
 */
export function isValidSearchResult(result: any): result is SearchMetadata {
  return (
    result &&
    typeof result === 'object' &&
    typeof result.keyword === 'string' &&
    typeof result.url === 'string' &&
    Array.isArray(result.snippets) &&
    typeof result.timestamp === 'string' &&
    result.source === 'brave_search'
  )
}
