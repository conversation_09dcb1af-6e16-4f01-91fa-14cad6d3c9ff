'use client'

import React from 'react'
import { <PERSON><PERSON><PERSON>, FiArrowDown } from 'react-icons/fi'
import type { UIMessage } from 'ai'
import {
  Conversation,
  ConversationContent,
  ConversationScrollButton,
} from '@/components/ai-elements/conversation'
import { Message, MessageContent } from '@/components/ai-elements/message'
import { Response } from '@/components/ai-elements/response'
import {
  Tool,
  ToolHeader,
  ToolContent,
  ToolInput,
  ToolOutput,
} from '@/components/ai-elements/tool'
import {
  Reasoning,
  ReasoningTrigger,
  ReasoningContent,
} from '@/components/ai-elements/reasoning'

type ChatV2MessageListProps = {
  messages: UIMessage[]
  isLoading?: boolean
  className?: string
}

// Helper function to render tool calls in Cursor-style (subtle, compact)
const renderCursorStyleTools = (toolParts: any[], messageId: string) => {
  // Group tool calls and results together, but also handle unknown tool types
  const toolGroups = new Map<
    string,
    { call?: any; result?: any; others?: any[] }
  >()

  toolParts.forEach(part => {
    if (part.type === 'tool-call') {
      const toolCallId = part.toolCallId || part.id || 'unknown'
      if (!toolGroups.has(toolCallId)) {
        toolGroups.set(toolCallId, {})
      }
      toolGroups.get(toolCallId)!.call = part
    } else if (part.type === 'tool-result') {
      const toolCallId = part.toolCallId || part.id || 'unknown'
      if (!toolGroups.has(toolCallId)) {
        toolGroups.set(toolCallId, {})
      }
      toolGroups.get(toolCallId)!.result = part
    } else {
      // Handle any other tool-related parts (unknown types)
      const toolCallId =
        part.toolCallId || part.id || `unknown-${part.type}-${Date.now()}`
      if (!toolGroups.has(toolCallId)) {
        toolGroups.set(toolCallId, { others: [] })
      }
      if (!toolGroups.get(toolCallId)!.others) {
        toolGroups.get(toolCallId)!.others = []
      }
      toolGroups.get(toolCallId)!.others!.push(part)
    }
  })

  return Array.from(toolGroups.entries()).map(([toolCallId, group], index) => {
    const toolCall = group.call
    const toolResult = group.result
    const otherParts = group.others || []

    // Determine tool name from any available source
    const toolName =
      toolCall?.toolName ||
      toolCall?.type ||
      otherParts[0]?.toolName ||
      otherParts[0]?.type ||
      'tool'

    const hasError = toolResult?.errorText
    const isComplete = toolResult && !hasError

    return (
      <div
        key={`${messageId}-tool-group-${toolCallId}-${index}`}
        className="border border-gray-200 rounded-md bg-gray-50/30 text-xs"
      >
        <Tool>
          <ToolHeader
            type={toolName}
            state={
              isComplete
                ? 'output-available'
                : hasError
                  ? 'output-error'
                  : 'input-available'
            }
            className="px-2 py-1.5 text-xs hover:bg-gray-100/30"
          />
          <ToolContent>
            {toolCall?.args && (
              <div className="px-2 pb-1">
                <ToolInput input={toolCall.args} />
              </div>
            )}
            {toolResult && (
              <div className="px-2 pb-1">
                <ToolOutput
                  output={toolResult.result}
                  errorText={toolResult.errorText}
                />
              </div>
            )}
            {/* Render unknown/other tool parts */}
            {otherParts.map((part, partIndex) => (
              <div
                key={`${toolCallId}-other-${partIndex}`}
                className="px-2 pb-1"
              >
                <div className="text-xs text-gray-600 font-mono bg-gray-50/50 px-1.5 py-1 rounded">
                  <div className="font-semibold text-gray-700 mb-1">
                    {part.type || 'unknown-tool-part'}
                  </div>
                  <div className="text-gray-600">
                    {JSON.stringify(part, null, 2)}
                  </div>
                </div>
              </div>
            ))}
          </ToolContent>
        </Tool>
      </div>
    )
  })
}

export default function ChatV2MessageList({
  messages,
  isLoading = false,
  className = '',
}: ChatV2MessageListProps) {
  // StickToBottom handles scrolling within the chat container.
  // Avoid window-level scrollIntoView to prevent the entire page from jumping.

  // Filter out system messages for display
  const displayMessages = messages.filter(message => message.role !== 'system')

  // Helper function to group tool calls and results together
  const groupToolParts = (parts: any[]) => {
    const textParts: any[] = []
    const toolParts: any[] = []
    const reasoningParts: any[] = []

    parts.forEach(part => {
      if (part.type === 'tool-call' || part.type === 'tool-result') {
        toolParts.push(part)
      } else if (part.type === 'reasoning') {
        reasoningParts.push(part)
      } else {
        textParts.push(part)
      }
    })

    return { textParts, toolParts, reasoningParts }
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      <Conversation className="flex-1">
        <ConversationContent>
          {displayMessages.length === 0 ? (
            <div className="flex items-center justify-center h-full text-gray-500">
              <div className="text-center">
                <FiCpu className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p className="text-lg font-medium">Start a conversation</p>
                <p className="text-sm">
                  Send a message to begin chatting with AI
                </p>
              </div>
            </div>
          ) : (
            displayMessages.map((message, messageIndex) => {
              // Normalize to parts array for rendering. Some client messages may only have `content`.
              const partsForGrouping: any[] = (() => {
                const m: any = message as any
                if (Array.isArray(m.parts) && m.parts.length > 0) return m.parts
                if (
                  typeof m.content === 'string' &&
                  m.content.trim().length > 0
                ) {
                  return [{ type: 'text', text: m.content }]
                }
                if (Array.isArray(m.content)) {
                  return m.content.map((c: any) =>
                    typeof c === 'string' ? { type: 'text', text: c } : c
                  )
                }
                return []
              })()

              // Group message parts for better organization
              const { textParts, toolParts, reasoningParts } =
                groupToolParts(partsForGrouping)

              return (
                <Message key={message.id} from={message.role}>
                  <MessageContent>
                    {/* Render tool calls first to show logical flow */}
                    {toolParts.length > 0 && (
                      <div className="mb-3 space-y-1">
                        {renderCursorStyleTools(toolParts, message.id)}
                      </div>
                    )}

                    {/* Render reasoning parts (for o1-series models) */}
                    {reasoningParts.map((part, partIndex) => (
                      <Reasoning
                        key={`${message.id}-reasoning-${partIndex}`}
                        isStreaming={
                          isLoading &&
                          messageIndex === displayMessages.length - 1
                        }
                        defaultOpen={false}
                      >
                        <ReasoningTrigger />
                        <ReasoningContent>{part.text}</ReasoningContent>
                      </Reasoning>
                    ))}

                    {/* Render main text content */}
                    {textParts.map((part, partIndex) => {
                      // Handle text parts
                      if (part.type === 'text') {
                        return (
                          <Response
                            key={`${message.id}-text-${partIndex}`}
                            defaultOrigin={
                              typeof window !== 'undefined'
                                ? window.location.origin
                                : undefined
                            }
                          >
                            {part.text}
                          </Response>
                        )
                      }

                      // Handle text-delta parts (streaming text)
                      if ((part as any).type === 'text-delta') {
                        return (
                          <Response
                            key={`${message.id}-text-delta-${partIndex}`}
                            defaultOrigin={
                              typeof window !== 'undefined'
                                ? window.location.origin
                                : undefined
                            }
                          >
                            {(part as any).textDelta}
                          </Response>
                        )
                      }

                      // Handle unknown part types gracefully
                      console.warn(
                        'Unknown message part type:',
                        part.type,
                        part
                      )
                      return null
                    })}

                    {/* Show loading indicator for streaming messages */}
                    {isLoading &&
                      messageIndex === displayMessages.length - 1 && (
                        <div className="flex items-center gap-2 text-xs text-gray-500 mt-2">
                          <div className="flex space-x-1">
                            <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" />
                            <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce delay-100" />
                            <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce delay-200" />
                          </div>
                          <span>AI is thinking...</span>
                        </div>
                      )}
                  </MessageContent>
                </Message>
              )
            })
          )}

          {/* StickToBottom internally manages the scroll anchor */}
        </ConversationContent>

        {/* Scroll to bottom button */}
        <ConversationScrollButton>
          <FiArrowDown className="w-4 h-4" />
        </ConversationScrollButton>
      </Conversation>
    </div>
  )
}
