'use client'

import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react'
import {
  FiMessageCircle,
  FiSettings,
  FiCpu,
  FiEdit2,
  FiCopy,
} from 'react-icons/fi'
import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { getAccessPermissions } from '@/app/configs/tier-permissions'
import { useSession } from 'next-auth/react'
import { SubscriptionTier } from '@prisma/client'
import { useChat } from '@ai-sdk/react'
import { DefaultChatTransport } from 'ai'

import type { UIMessage } from 'ai'
import type { Tab } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useTabStore'
import { getChatApiEndpoint } from '@/app/configs/feature-flags'

import { useTabStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useTabStore'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { useNavigationStore } from '@/app/stores/navigation_store'
import toast from 'react-hot-toast'

import ChatV2MessageList from '../chat/ChatV2MessageList'
import ChatV2Input from '../chat/ChatV2Input'

type ChatTabContentV2Props = {
  tab: Tab
  dragTreeId: string
}

// Internal component that uses the ChatPane context
function ChatTabContentV2Internal({
  tab,
  conversationTitle,
  contextIdsFromConversation,
}: {
  tab: Tab
  conversationTitle?: string | null
  contextIdsFromConversation?: string[] // Fallback from metadata to avoid "No context" mismatch
}) {
  const { data: session } = useSession()
  const userTier =
    (session?.user as any)?.subscriptionTier || SubscriptionTier.FREE
  const permissions = getAccessPermissions(userTier)

  // Get tree data for finding node names
  const findNodeById = useDragTreeStore(state => state.findNodeById)
  const navigateToTreeNode = useNavigationStore(
    state => state.navigateToTreeNode
  )
  const updateTabTitle = useTabStore(state => state.updateTabTitle)

  // Compute effective context IDs: prefer tab.aiPaneData, fallback to conversation metadata for consistency
  const effectiveContextIds = useMemo<string[]>(() => {
    const idsFromTab = tab.aiPaneData?.contextIds || []
    if (idsFromTab.length > 0) return idsFromTab
    return contextIdsFromConversation || []
  }, [tab.aiPaneData?.contextIds, contextIdsFromConversation])

  // Initialize all hooks first (before any conditional returns)
  // Remove local messages state - use chat's state for everything
  const [isLoadingHistory, setIsLoadingHistory] = useState<boolean>(true)
  const conversationId = tab.aiPaneData?.conversationId

  // Add ref to track if history has been set (prevents re-setting during streaming)
  const historySetRef = useRef(false)
  const [showSettingsModal, setShowSettingsModal] = useState<boolean>(false)
  const [showContextDialog, setShowContextDialog] = useState<boolean>(false)
  const [isEditingTitle, setIsEditingTitle] = useState<boolean>(false)
  const [editableTitle, setEditableTitle] = useState<string>(tab.title || '')

  useEffect(() => {
    setEditableTitle(tab.title || '')
  }, [tab.title])

  // Input state management (AI SDK v5 doesn't provide this)
  const [input, setInput] = useState<string>('')

  // Cache configuration
  const CACHE_TTL = 2 * 60 * 1000 // 2 minutes

  // Enhanced cache functions with localStorage persistence
  const loadFromCache = useCallback(
    (id: string) => {
      try {
        const cachedStr = localStorage.getItem(`chat-cache-${id}`)
        if (cachedStr) {
          const cached = JSON.parse(cachedStr)
          if (Date.now() - cached.timestamp < CACHE_TTL) {
            return cached.data
          }
        }
      } catch (error) {
        console.warn('[Chat Cache] Failed to load from localStorage:', error)
      }
      return null
    },
    [CACHE_TTL]
  )

  const saveToCache = useCallback((id: string, data: UIMessage[]) => {
    try {
      localStorage.setItem(
        `chat-cache-${id}`,
        JSON.stringify({
          data,
          timestamp: Date.now(),
          lastMessageId: data[data.length - 1]?.id || null,
        })
      )
    } catch (error) {
      console.warn('[Chat Cache] Failed to save to localStorage:', error)
    }
  }, [])

  const invalidateCache = useCallback((id: string) => {
    try {
      localStorage.removeItem(`chat-cache-${id}`)
    } catch (error) {
      console.warn('[Chat Cache] Failed to invalidate localStorage:', error)
    }
  }, [])

  const contextContent = useCallback(() => {
    try {
      if (effectiveContextIds.length === 0) return ''
      return `Context from ${effectiveContextIds.length} documents`
    } catch (error) {
      console.error('Error getting context:', error)
      return ''
    }
  }, [effectiveContextIds])

  // Get context document names for display
  const getContextDisplayText = useCallback(() => {
    if (effectiveContextIds.length === 0) return 'No context documents selected'

    if (effectiveContextIds.length === 1) {
      const node = findNodeById(effectiveContextIds[0])
      const nodeName = node?.label || 'Document'
      return `Using 1 context document: ${nodeName}`
    }

    if (effectiveContextIds.length <= 3) {
      const nodeNames = effectiveContextIds
        .map(id => findNodeById(id)?.label || 'Document')
        .join(', ')
      return `Using ${effectiveContextIds.length} context documents: ${nodeNames}`
    }

    // For more than 3 documents, show count only
    return `Using ${effectiveContextIds.length} context documents`
  }, [effectiveContextIds, findNodeById])

  // Auto-fetch context if missing (debug and recovery)
  useEffect(() => {
    console.log('[Context Debug] Current state:', {
      'tab.id': tab.id,
      'effectiveContextIds.length': effectiveContextIds.length,
      effectiveContextIds: effectiveContextIds,
      'tab.aiPaneData?.contextIds': tab.aiPaneData?.contextIds,
      contextIdsFromConversation: contextIdsFromConversation,
    })

    // Skip if we already have context
    if (effectiveContextIds.length > 0) return

    // For now, just log the debug info - in a real implementation,
    // you would fetch from an API endpoint like `/api/context/${dragTreeId}`
    console.log(
      '[Context Debug] No context found - would fetch from API if endpoint existed'
    )
  }, [tab.id, effectiveContextIds, contextIdsFromConversation])

  // Only initialize chat when we have a conversation ID
  const shouldMountChat = Boolean(conversationId)

  // Track first turn to decide whether to include system context in payload
  const isFirstTurnRef = useRef(true)

  // Memoize context content to prevent re-computation
  const memoizedContextContent = useMemo(() => {
    return contextContent()
  }, [contextContent])

  // Memoize transport to prevent re-initialization on every render
  const transport = useMemo((): DefaultChatTransport<UIMessage> | undefined => {
    if (!shouldMountChat) return undefined

    return new DefaultChatTransport({
      api: getChatApiEndpoint(),
      prepareSendMessagesRequest: ({ messages: outgoing }): { body: any } => {
        // Extract text from the last message (user's input)
        const lastMessage = outgoing[outgoing.length - 1] as any
        let text = ''

        if (Array.isArray(lastMessage?.parts)) {
          const textPart = lastMessage.parts.find(
            (p: any) => p?.type === 'text'
          )
          text = textPart?.text || ''
        } else if (typeof lastMessage?.content === 'string') {
          text = lastMessage.content
        }

        if (!conversationId) {
          throw new Error('Conversation ID not available')
        }

        const body = {
          message: text,
          conversationId,
          model: tab.aiPaneData?.model || 'gpt-4.1',
          contextIds: effectiveContextIds,
          // Include context only on first message
          ...(isFirstTurnRef.current && {
            context: memoizedContextContent,
          }),
        }

        return { body }
      },
    })
  }, [
    shouldMountChat,
    conversationId,
    tab.aiPaneData?.model,
    effectiveContextIds,
    memoizedContextContent,
  ])

  // Use proper transport configuration for AI SDK v5
  const chat = useChat({
    id: conversationId || 'disabled',
    transport,
  })

  console.log('[ChatV2] useChat debug:', {
    'chat keys': Object.keys(chat),
    'chat.messages.length': chat.messages.length,
    'chat.status': chat.status,
  })

  // Input change handler
  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      setInput(e.target.value)
    },
    []
  )

  // Submit handler - only works when conversation ID exists
  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault()
      if (!input.trim() || !conversationId) return

      // Use AI SDK v5 sendMessage method (only when chat is ready)
      if (shouldMountChat && chat.sendMessage) {
        // After first user send, no longer attach initial context in payload
        isFirstTurnRef.current = false
        chat.sendMessage({ text: input.trim() })

        // Invalidate cache on send for freshness
        invalidateCache(conversationId)
        console.log(`🗑️ [Chat Cache] Invalidated cache for ${conversationId}`)

        setInput('') // Clear input after sending
      }
    },
    [input, conversationId, shouldMountChat, chat.sendMessage, invalidateCache]
  )

  const handleTitleSave = useCallback(() => {
    const title = editableTitle.trim()
    if (title.length === 0) {
      setIsEditingTitle(false)
      return
    }
    updateTabTitle(tab.id, title)
    if (conversationId) {
      fetch(`/api/aipane/conversations/${conversationId}/title`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ title }),
      }).catch(err => console.error('Failed to update title:', err))
    }
    setIsEditingTitle(false)
  }, [editableTitle, updateTabTitle, tab.id, conversationId])

  // Load history on mount or conversationId change with caching
  useEffect(() => {
    if (!conversationId) return
    setIsLoadingHistory(true)

    const loadHistory = async () => {
      const startTime = performance.now()

      try {
        // Check localStorage cache first
        const cachedData = loadFromCache(conversationId)
        if (cachedData) {
          const loadTime = performance.now() - startTime
          console.log(
            `⚡ [Chat Cache] Hit for ${conversationId} (~${loadTime.toFixed(2)}ms)`
          )
          // Ensure cached data has proper parts structure for ChatV2MessageList
          const normalizedCachedData = cachedData.map((msg: any) => ({
            ...msg,
            // Add parts structure if missing (for backward compatibility)
            parts:
              msg.parts ||
              (msg.content ? [{ type: 'text', text: msg.content }] : []),
          }))

          // Integrate cached history into useChat (prevents re-setting during streaming)
          if (!historySetRef.current) {
            chat.setMessages(normalizedCachedData)
            historySetRef.current = true
            if (normalizedCachedData.length > 0) {
              isFirstTurnRef.current = false
            }
          }
          setIsLoadingHistory(false)
          await new Promise(resolve => setTimeout(resolve, 0))
          return
        }

        // Cache miss or stale - fetch from API
        const url = new URL(
          `/api/aipane/conversations/${conversationId}`,
          window.location.origin
        )
        url.searchParams.set('limit', '50')
        url.searchParams.set('includeSteps', 'true') // Include steps for tool calls
        const response = await fetch(url.toString())
        if (!response.ok) throw new Error('Failed to load history')

        const data = await response.json()

        // Convert history to AI SDK v5 format using uiMessage directly
        const historyMessages: UIMessage[] = (data.messages || []).map(
          (msg: any) => msg.uiMessage as UIMessage
        )

        // Integrate history into useChat (prevents re-setting during streaming)
        if (!historySetRef.current) {
          chat.setMessages(historyMessages)
          historySetRef.current = true
          if (historyMessages.length > 0) {
            isFirstTurnRef.current = false
          }
        }

        // Store in localStorage cache
        saveToCache(conversationId, historyMessages)

        const loadTime = performance.now() - startTime
        console.log(
          `📚 [Chat Cache] Miss - Stored new data for ${conversationId} (${loadTime.toFixed(2)}ms)`
        )
      } catch (error) {
        console.error('Failed to load history:', error)
      } finally {
        setIsLoadingHistory(false)
      }
    }

    loadHistory()
  }, [conversationId])

  // Use AI SDK messages directly - no manual merging needed
  // The AI SDK v5 handles message persistence and tool calls automatically
  const displayMessages = chat.messages

  // Check permissions after hooks are initialized
  if (!permissions.canCreateAiChat) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <FiMessageCircle className="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <h2 className="text-lg font-medium text-gray-500 mb-2">
            Chat Not Available
          </h2>
          <p className="text-sm text-gray-400">
            Upgrade your plan to access AI chat functionality
          </p>
        </div>
      </div>
    )
  }

  const showLoading = isLoadingHistory && displayMessages.length === 0
  const isEmpty = !showLoading && displayMessages.length === 0

  return (
    <div className="flex flex-col h-full min-h-0">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <FiMessageCircle className="w-5 h-5 text-blue-600" />
          <div>
            {isEditingTitle ? (
              <Input
                value={editableTitle}
                onChange={e => setEditableTitle(e.target.value)}
                onBlur={handleTitleSave}
                onKeyDown={e => {
                  if (e.key === 'Enter') {
                    e.preventDefault()
                    handleTitleSave()
                  }
                }}
                className="h-7 text-sm px-2 py-1"
                autoFocus
              />
            ) : (
              <div className="flex items-center space-x-1 group">
                <h2 className="font-medium text-gray-900">
                  {(conversationTitle && conversationTitle.trim()) ||
                    (editableTitle && editableTitle.trim()) ||
                    'Untitled'}{' '}
                  {/* Prefer backend title, fallback to simple 'Untitled' without prefix */}
                </h2>
                <button
                  onClick={() => setIsEditingTitle(true)}
                  className="opacity-0 group-hover:opacity-100 transition-opacity text-gray-500 hover:text-gray-700"
                  title="Edit title"
                >
                  <FiEdit2 className="w-4 h-4" />
                </button>
              </div>
            )}
            <p className="text-xs text-gray-500">
              {getContextDisplayText()}
              {effectiveContextIds.length === 0 && (
                <span className="ml-2 text-gray-400">
                  (Debug: tab.id={tab.id.slice(-8)}, store check needed)
                </span>
              )}
            </p>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          className="text-gray-500 hover:text-gray-700"
          onClick={() => setShowSettingsModal(true)}
          title="Chat settings"
        >
          <FiSettings className="w-4 h-4" />
        </Button>
      </div>

      {/* Body */}
      <div className="flex-1 min-h-0">
        {showLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="flex flex-col items-center gap-2 text-gray-500 animate-pulse">
              <FiMessageCircle className="w-8 h-8" />
              <span className="text-sm">Loading conversation...</span>
            </div>
          </div>
        ) : isEmpty ? (
          <div className="flex h-full items-center justify-center">
            <div className="text-center max-w-md">
              <FiCpu className="w-16 h-16 mx-auto mb-6 text-gray-300" />
              <h3 className="text-xl font-medium text-gray-900 mb-3">
                Start a Conversation
              </h3>
              <p className="text-gray-500 mb-6">
                Chat with AI to get help with your questions. The AI has access
                to web search and can provide comprehensive answers.
              </p>
            </div>
          </div>
        ) : (
          <ChatV2MessageList
            messages={displayMessages}
            isLoading={
              chat.status === 'streaming' || chat.status === 'submitted'
            }
            className="flex-1 min-h-0"
          />
        )}
      </div>

      {/* Input */}
      <ChatV2Input
        input={input}
        onInputChange={handleInputChange}
        onSubmit={handleSubmit}
        disabled={!permissions.canCreateAiMessage || !conversationId}
        isLoading={chat.status === 'streaming' || chat.status === 'submitted'}
        placeholder={
          !conversationId
            ? 'Start a chat to begin messaging...'
            : isEmpty
              ? 'Type your question...'
              : 'Message AI Assistant...'
        }
      />

      {/* Settings Modal */}
      <Dialog open={showSettingsModal} onOpenChange={setShowSettingsModal}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Chat Settings</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="text-sm flex items-center justify-between">
              <span className="font-medium text-gray-700">Model</span>
              <span className="text-gray-900">
                {tab.aiPaneData?.model || 'gpt-4.1'}
              </span>
            </div>

            <div className="text-sm flex items-center justify-between">
              <span className="font-medium text-gray-700">Messages</span>
              <span className="text-gray-900">{displayMessages.length}</span>
            </div>

            <div className="text-sm flex items-center justify-between">
              <span className="font-medium text-gray-700">Conversation ID</span>
              <div className="flex items-center space-x-2">
                <span className="text-gray-900 font-mono text-xs">
                  {conversationId || 'Not created yet'}
                </span>
                {conversationId && (
                  <button
                    onClick={() => {
                      navigator.clipboard.writeText(conversationId)
                      toast.success('Conversation ID copied!')
                    }}
                    className="text-gray-500 hover:text-gray-700"
                    title="Copy conversation ID"
                  >
                    <FiCopy className="w-3 h-3" />
                  </button>
                )}
              </div>
            </div>

            {effectiveContextIds.length > 0 && (
              <div className="text-sm flex items-center justify-between">
                <span className="font-medium text-gray-700">Context Items</span>
                <button
                  onClick={() => {
                    setShowContextDialog(true)
                    setShowSettingsModal(false)
                  }}
                  className="text-blue-600 underline hover:text-blue-800"
                >
                  {effectiveContextIds.length} selected
                </button>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Context Items Dialog */}
      <Dialog open={showContextDialog} onOpenChange={setShowContextDialog}>
        <DialogContent className="max-w-2xl max-h-[70vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>
              Context Items ({effectiveContextIds.length})
            </DialogTitle>
          </DialogHeader>
          <div className="overflow-y-auto space-y-2 max-h-[60vh]">
            {effectiveContextIds.map((nodeId, index) => {
              const node = findNodeById(nodeId)
              const nodeLabel = node?.label || `Item ${index + 1}`

              return (
                <div
                  key={nodeId}
                  className="p-3 border border-gray-200 rounded-lg bg-gray-50"
                >
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-gray-900">{nodeLabel}</h4>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        navigateToTreeNode(nodeId)
                        setShowContextDialog(false)
                      }}
                    >
                      View
                    </Button>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    Node ID: {nodeId}
                  </p>
                </div>
              )
            })}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}

// Main export component that wraps with provider
export default function ChatTabContentV2({
  tab,
  dragTreeId: _dragTreeId,
}: ChatTabContentV2Props) {
  // const { updateTabAiPaneData } = useTabStore() // Disabled with auto-fix logic

  // Get conversation data to check for contextIds in metadata
  const [conversationData, setConversationData] = useState<any>(null)

  // Load conversation data to check metadata
  useEffect(() => {
    if (tab.aiPaneData?.conversationId) {
      fetch(`/api/aipane/conversations/${tab.aiPaneData.conversationId}`)
        .then(res => res.json())
        .then(data => setConversationData(data?.conversation || null))
        .catch(err => console.error('Failed to load conversation:', err))
    }
  }, [tab.aiPaneData?.conversationId])

  // Parse conversation metadata
  let _meta: any = conversationData?.metadata
  if (typeof _meta === 'string') {
    try {
      _meta = JSON.parse(_meta)
    } catch {
      _meta = undefined
    }
  }
  const contextIdsFromConversation = Array.isArray(_meta?.contextIds)
    ? (_meta.contextIds as string[])
    : []

  return (
    <ChatTabContentV2Internal
      tab={tab}
      conversationTitle={conversationData?.title ?? null}
      contextIdsFromConversation={contextIdsFromConversation}
    />
  )
}
