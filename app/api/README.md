# API Documentation

This document provides comprehensive documentation for API endpoints. The API is divided into two main categories: **V2 (Current)** endpoints for the new drag tree system, and **Legacy (V1)** endpoints for the old conversation system.

## Table of Contents

- [V2 API Endpoints (Current)](#v2-api-endpoints-current)
  - [Authentication](#authentication)
  - [Drag Tree Management](#drag-tree-management)
  - [Screening](#screening)
  - [Payments](#payments)
- [Legacy V1 API Endpoints](#legacy-v1-api-endpoints)
- [Common Patterns](#common-patterns)
- [Error Handling](#error-handling)

---

## V2 API Endpoints (Current)

These are the actively maintained endpoints for the new drag tree clarification system.

### Authentication

#### `/api/auth/[...nextauth]` - NextAuth.js Authentication

**Method:** `GET`, `POST`
**Description:** Handles authentication via NextAuth.js with Google/GitHub OAuth providers
**Configuration:** See root `auth.ts` for provider settings and callbacks

---

### Drag Tree Management

The drag tree system is the core V2 feature for hierarchical question clarification.

#### `/api/dragtree/initialize` - Initialize New Drag Tree

**Method:** `POST`
**Description:** Creates a new drag tree record in INITIALIZED status
**Validation:** Uses `initializeDragTreeSchema` for request validation

**Request Body:**

```typescript
{
  title: string;                    // 1-255 characters
  description: string;              // Required, user's problem description
  preferredLanguage?: string;       // Language code, defaults to "en"
  analysisData?: {                  // Optional screening analysis data
    is_problem_clear: boolean;
    problem_ambiguity: string;
    intention: string[];
    entity: string[];
    score: number;
    pass: boolean;
  } | string[];                     // Legacy array format also supported
}
```

**Response:**

```typescript
{
  success: true
  data: {
    dragTreeId: string
    status: 'INITIALIZED'
    title: string
    description: string
    message: string
  }
}
```

**Authentication:** Required - validates user session and active status
**Ownership:** Creates tree owned by authenticated user

---

#### `/api/dragtree/generate_questions` - Generate Question Tree

**Method:** `POST`
**Description:** Generates hierarchical questions using AI based on user's problem description
**Validation:** Uses `generateQuestionsSchema` for request validation
**Max Duration:** 60 seconds (streaming response)

**Request Body:**

```typescript
{
  dragTreeId: string;              // Required, must be in INITIALIZED status
  regenerate?: boolean;            // Optional, defaults to false
}
```

**Response:** Streaming text response with real-time question generation
**Final Update:** Updates drag tree status to ACTIVE with generated structure

**Authentication:** Required
**Ownership:** Validates user owns the drag tree
**Status Check:** Tree must be in INITIALIZED status
**AI Logging:** Full audit trail with entityType, messages, and metadata

---

#### `/api/dragtree/load` - Load Existing Drag Tree

**Method:** `GET`
**Description:** Retrieves complete drag tree data including nodes and structure
**Validation:** Uses `loadDragTreeSchema` for query parameter validation

**Query Parameters:**

```typescript
{
  id: string // Required drag tree ID
}
```

**Response:**

```typescript
{
  success: true;
  data: {
    dragTree: DragTree;            // Complete tree metadata
    nodes: DragTreeNode[];         // All tree nodes
    structure: TreeStructure;      // Hierarchical structure
  }
}
```

**Authentication:** Required
**Ownership:** Validates user owns the drag tree

---

#### `/api/dragtree/list` - List User's Drag Trees

**Method:** `GET`
**Description:** Returns paginated list of user's drag trees
**Validation:** Uses `listDragTreesSchema` for query parameters

**Query Parameters:**

```typescript
{
  page?: number;                   // Page number, defaults to 1
  limit?: number;                  // Items per page (1-100), defaults to 10
  status?: DragTreeStatus;         // Optional status filter
  search?: string;                 // Optional search term
}
```

**Response:**

```typescript
{
  success: true;
  data: {
    dragTrees: DragTree[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    }
  }
}
```

**Authentication:** Required
**User Status:** Validates user is ACTIVE

---

#### `/api/dragtree/research_create` - Create Research Content

**Method:** `POST`, `PATCH`
**Description:** Creates or updates research content for drag tree nodes

**POST - Create Research:**

```typescript
{
  nodeId: string;                  // Target node ID
  content: string;                 // Research content
  type?: "RESEARCH" | "QUESTION" | "ANSWER"; // Defaults to "RESEARCH"
}
```

**PATCH - Update Status:**

```typescript
{
  nodeId: string
  status: DragTreeNodeStatus // New status for the node
}
```

**Authentication:** Required
**Ownership:** Validates user owns the node through drag tree

---

#### `/api/dragtree/research_generate` - AI Research Generation

**Method:** `POST`
**Description:** Generates research content for nodes using AI with web search
**Max Duration:** 60 seconds (streaming response)

**Request Body:**

```typescript
{
  nodeId: string;                  // Target node ID
  query: string;                   // Research query
  messages?: Array<{              // Optional, for useChat format
    role: "user" | "assistant" | "system";
    content: string;
  }>;
}
```

**Response:** Streaming text with research content
**Features:** Brave Search integration, comprehensive web research
**AI Logging:** Full audit trail with search metadata

**Authentication:** Required
**Ownership:** Validates user owns the node

---

### Screening

The screening system analyzes and rephrases user problems before creating drag trees.

#### `/api/screening/diagnose` - Analyze Problem Clarity

**Method:** `POST`
**Description:** Analyzes user's problem description for clarity and completeness
**Validation:** Uses `screeningRequestSchema` for request validation
**Max Duration:** 60 seconds (streaming response)

**Request Body:**

```typescript
{
  userId: string;                  // Required user ID
  description: string;             // Required problem description
  preferredLanguage?: string;      // Optional language code
}
```

**Response:** Streaming response with final structured analysis:

```typescript
{
  is_problem_clear: boolean;
  problem_ambiguity: string;
  intention: string[];             // Identified user intentions
  entity: string[];               // Key entities/stakeholders
  score: number;                  // Clarity score
  pass: boolean;                  // Whether problem is clear enough
}
```

**AI Model:** `gpt-4.1-mini` via Azure OpenAI
**AI Logging:** Full audit trail with structured metadata

---

#### `/api/screening/rephrase` - Rephrase Problem Statements

**Method:** `POST`
**Description:** Generates 4 different rephrased versions of user's problem
**Validation:** Uses `screeningRequestSchema` for request validation
**Max Duration:** 60 seconds (streaming response)

**Request Body:**

```typescript
{
  userId: string;                  // Required user ID
  description: string;             // Required problem description
  preferredLanguage?: string;      // Optional language code
}
```

**Response:** Streaming response with 4 rephrased problem statements
**Format:** Each statement includes focus label and targets different aspects

**AI Model:** `gpt-4.1-mini` via Azure OpenAI
**AI Logging:** Full audit trail with rephrasing metadata

---

### Payments

#### `/api/payments/webhook` - Stripe Webhook Handler

**Method:** `POST`
**Description:** Handles Stripe webhook events for subscription management
**Security:** Validates webhook signature using Stripe secret
**Events:** Processes subscription creation, updates, and cancellations

---

## Legacy V1 API Endpoints

These endpoints support the old conversation-based system and are maintained for backward compatibility.

### Conversations

- `/api/conversations` - CRUD operations for conversations
- `/api/conversations/[conversationId]` - Individual conversation management
- `/api/messages` - Message handling within conversations

### Issue Trees (Legacy)

- `/api/issuetree/generate_questions` - Generate questions for issue trees
- `/api/issuetree/load` - Load existing issue trees
- `/api/issuetree/update` - Update issue tree data

### Legacy Features

- `/api/chat` - Real-time chat functionality
- `/api/conversation` - Conversation management
- `/api/email` - Email notifications
- `/api/prompt` - Prompt management
- `/api/report` - Report generation
- `/api/generate` - Content generation
- `/api/rephrase` - Text rephrasing (superseded by screening)

### RAG (Retrieval Augmented Generation)

- `/api/rag/assistant` - RAG-powered assistance
- `/api/rag/search` - Document search
- `/api/rag/generate_response` - Generate responses with RAG
- `/api/rag/save_response` - Save RAG responses

### Subtrees

- `/api/subtree/generate_questions` - Generate questions for subtrees
- `/api/subtree/load` - Load subtree data
- `/api/subtree/update` - Update subtree structure

### Notebooks

- `/api/notebook/generate_notebook` - Generate notebook content

---

## Common Patterns

### Authentication

All V2 endpoints use centralized authentication validation:

```typescript
const authResult = await validateAuthentication()
if (!authResult.success) {
  return authResult.error // 401 or 403
}
```

### Request Validation

V2 endpoints use Zod schemas for type-safe validation:

```typescript
const bodyResult = await validateRequestBody(request, schema)
if (!bodyResult.success) {
  return bodyResult.error // 400 with detailed errors
}
```

### Ownership Validation

Resource access is validated through ownership checks:

```typescript
const validationResult = await validateDragTreeOperation(
  dragTreeId,
  requiredStatus
)
if (!validationResult.success) {
  return validationResult.error // 403 or 404
}
```

### AI Usage Logging

All AI interactions are logged for audit purposes:

```typescript
await createAIUsage({
  userId,
  entityType: 'drag_tree',
  entityId: dragTreeId,
  aiProvider: 'azure_openai',
  modelName,
  usageType: AIUsageType.GENERATE_QUESTION,
  inputPrompt,
  messages,
  metadata: { tokenUsage, language },
  config: {},
})
```

---

## Error Handling

### Standard Error Response Format

```typescript
{
  error: string | object // Error message or validation details
}
```

### HTTP Status Codes

- `200` - Success
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (no session)
- `403` - Forbidden (inactive user or no ownership)
- `404` - Not Found (resource doesn't exist)
- `409` - Conflict (invalid status transition)
- `500` - Internal Server Error

### Validation Error Format

When using Zod schemas, validation errors are flattened:

```typescript
{
  error: {
    formErrors: string[];
    fieldErrors: {
      [fieldName]: string[];
    }
  }
}
```

---

## Frontend Integration

### Shared Validation

The same Zod schemas are used on both frontend and backend:

```typescript
import { initializeDragTreeSchema } from '@/app/libs/api-schemas'

// Frontend validation before API call
const result = initializeDragTreeSchema.safeParse(data)
if (!result.success) {
  // Handle validation errors on frontend
  return
}
// Make API call with validated data
```

### Benefits

- **Consistent Validation** - Same rules on frontend and backend
- **Type Safety** - Full TypeScript integration
- **Reduced Server Load** - Invalid requests caught early
- **Better UX** - Immediate feedback on validation errors
