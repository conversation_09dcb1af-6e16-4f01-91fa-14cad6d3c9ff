import { NextResponse } from 'next/server'
import { auth } from '@/auth'
import { createAiConversation } from '@/app/server-actions/ai-chat'
import { standardErrors } from '../shared/errors'
import { getConversationsByContext } from '@/app/server-actions/ai-chat'
import prisma from '@/app/libs/prismadb'
import { SubscriptionTier } from '@prisma/client'
import { getTierPermissions } from '@/app/configs/tier-permissions'

export const maxDuration = 30

export async function POST(req: Request) {
  try {
    const session = await auth()
    if (!session?.user?.id) return standardErrors.unauthorized()

    const body = await req.json().catch(() => ({}))
    const {
      conversationId: requestedConversationId,
      contextEntityType,
      contextEntityId,
      title = 'AI Chat Session',
    } = body || {}

    // Enforce per-tier AI Pane chat conversation count per drag tree
    try {
      const userTier = await prisma.user.findUnique({
        where: { id: session.user.id },
        select: { subscription_tier: true },
      })
      const tier = userTier?.subscription_tier || SubscriptionTier.FREE
      const { aiPaneChatPerTree } = getTierPermissions(tier)

      if (contextEntityType === 'drag_tree' && contextEntityId) {
        const convCount = await prisma.aiConversation.count({
          where: {
            userId: session.user.id,
            contextEntityType: 'drag_tree',
            contextEntityId,
          },
        })
        if (convCount >= aiPaneChatPerTree) {
          return standardErrors.forbidden(
            tier === SubscriptionTier.FREE
              ? 'Free plan limit reached: You can start up to 3 AI chats per drag tree. Upgrade to PRO to unlock more.'
              : 'You have reached the chat limit for this drag tree.'
          )
        }
      }
    } catch (e) {
      console.warn('Failed to enforce conversation limit', e)
    }

    const result = await createAiConversation(
      {
        userId: session.user.id,
        title,
        contextEntityType,
        contextEntityId,
        // Persist selected context IDs so the UI can read them from metadata
        metadata: {
          contextIds: Array.isArray((body as any)?.contextIds)
            ? (body as any).contextIds
            : [],
        },
      },
      requestedConversationId // optional exact id provided by client
    )

    if (!result.success)
      return standardErrors.internalError(result.error ?? 'create failed')

    return NextResponse.json({ conversationId: result.data!.conversationId })
  } catch (err) {
    console.error('Conversation create error', err)
    return standardErrors.internalError()
  }
}

/**
 * GET /api/aipane/conversations?contextEntityType=drag_tree&contextEntityId=tree_123
 * Returns array of conversation metadata (id, title, createdAt, updatedAt)
 */
export async function GET(req: Request) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })
    }

    const url = new URL(req.url)
    const contextEntityType = url.searchParams.get('contextEntityType')
    const contextEntityId = url.searchParams.get('contextEntityId')

    if (!contextEntityType || !contextEntityId) {
      return NextResponse.json(
        { message: 'Missing contextEntityType or contextEntityId' },
        { status: 400 }
      )
    }

    const result = await getConversationsByContext(
      contextEntityType,
      contextEntityId
    )

    if (!result.success) {
      return NextResponse.json({ message: result.error }, { status: 500 })
    }

    return NextResponse.json({ conversations: result.data })
  } catch (error) {
    console.error('List conversations error:', error)
    return NextResponse.json(
      { message: 'Error listing conversations' },
      { status: 500 }
    )
  }
}
