/**
 * Configuration constants for the AI Pane Chat API endpoint
 * Centralizes all magic numbers and settings for easier maintenance
 */

// Rate limiting configuration
export const RATE_LIMIT_CONFIG = {
  WINDOW_MS: 60000, // 1 minute window
  MAX_REQUESTS: 5, // 5 requests per minute
} as const

// AI Model configuration
export const MODEL_CONFIG = {
  DEFAULT_MODEL: 'gpt-4.1',
  MAX_STEPS: 10,
  TEMPERATURE: 0.7,
  MAX_TOKENS: 4000,
} as const

// Retry configuration
export enum RetryStrategy {
  LINEAR = 'LINEAR',
  EXPONENTIAL = 'EXPONENTIAL',
}

export const RETRY_CONFIG = {
  MAX_RETRIES: 3,
  BACKOFF_MS: 50,
  STRATEGY: RetryStrategy.EXPONENTIAL, // Default to exponential for resiliency
} as const

export const computeBackoffDelay = (
  attempt: number, // 1-based attempt counter
  baseMs: number,
  strategy: RetryStrategy
): number => {
  const safeAttempt = Math.max(1, attempt)
  if (strategy === RetryStrategy.EXPONENTIAL) {
    return baseMs * Math.pow(2, safeAttempt - 1)
  }
  // Linear fallback
  return baseMs * safeAttempt
}

// Tool configuration
export const TOOL_NAMES = {
  WEB_SEARCH: 'web_search',
} as const

// Execution step types for the collector
export enum ExecutionStepType {
  THOUGHT = 'THOUGHT',
  TOOL_CALL = 'TOOL_CALL',
  TOOL_RESULT = 'TOOL_RESULT',
  REASONING_SUMMARY = 'REASONING_SUMMARY',
  SUB_AGENT_INVOCATION = 'SUB_AGENT_INVOCATION',
}

// System prompt configuration
export const SYSTEM_PROMPT_CONFIG = {
  DEFAULT_SYSTEM_PROMPT: `You are a helpful AI assistant. You have access to web search capabilities to help answer questions with up-to-date information.

When using web search:
1. Use it when you need current information or when the user asks about recent events
2. Search for specific, relevant terms
3. Provide sources for the information you find
4. Synthesize information from multiple sources when appropriate

Always be helpful, accurate, and cite your sources when using web search results.`,
} as const

// Response configuration
export const RESPONSE_CONFIG = {
  // Maximum context length before truncation
  MAX_CONTEXT_LENGTH: 8000,
  // Default error messages
  DEFAULT_ERROR_MESSAGE: 'An error occurred while processing your request.',
} as const

// Supported AI models list
export const SUPPORTED_MODELS = ['gpt-4.1'] as const

export type SupportedModel = (typeof SUPPORTED_MODELS)[number]
