import { streamObject } from 'ai'
import { azure } from '@ai-sdk/azure'
import { z } from 'zod'
import { auth } from '@/auth'
import { updateConversationTitle } from '@/app/server-actions/ai-chat'
import { createAIUsage } from '@/app/server-actions/log_ai_usage'
import { AIUsageType } from '@prisma/client'
import { AiChatLogger } from '@/app/server-actions/ai-chat/logging'
import { standardErrors } from '@/app/api/aipane/shared/errors'
import {
  getLanguageName,
  type SupportedLanguageCode,
} from '@/app/constants/languages'

export const maxDuration = 30

// Schema for title generation response
const titleResponseSchema = z.object({
  title: z
    .string()
    .describe(
      'A concise, descriptive title for this conversation (maximum 20 words)'
    ),
})

// Request schema
const titleRequestSchema = z.object({
  conversationId: z.string().min(1, 'conversationId is required'),
  userPrompt: z.string().min(1, 'userPrompt is required'),
  contextQuestions: z.array(z.string()).optional().default([]),
  firstMessage: z.string().min(1, 'firstMessage is required'),
  preferredLanguage: z.string().optional().default('en'),
})

// Initialize logger
const titleLogger = new AiChatLogger('aipane-generate-title')

export async function POST(req: Request) {
  try {
    // Authentication
    const session = await auth()
    const userId = session?.user?.id

    if (!userId) {
      return standardErrors.unauthorized('Authentication required')
    }

    // Parse and validate request
    const body = await req.json()
    const validationResult = titleRequestSchema.safeParse(body)

    if (!validationResult.success) {
      titleLogger.warn('Invalid request data', {
        userId,
        metadata: { errors: validationResult.error.errors },
      })
      return standardErrors.invalidJson('Invalid request data')
    }

    const {
      conversationId,
      userPrompt,
      contextQuestions,
      firstMessage,
      preferredLanguage,
    } = validationResult.data

    titleLogger.info('Starting title generation', {
      userId,
      metadata: { conversationId, preferredLanguage },
    })

    // Build the prompt for title generation
    const contextSection =
      contextQuestions.length > 0
        ? `These are their current interested topics: ${contextQuestions.join(', ')}`
        : 'No specific topics selected.'

    const languageInstruction =
      preferredLanguage !== 'en'
        ? `Please generate the title in ${getLanguageName(preferredLanguage as SupportedLanguageCode)}.`
        : 'Please generate the title in English.'

    const systemPrompt = `You are an expert at creating concise, descriptive conversation titles.
Your task is to generate a title that captures the essence of what the user is asking about.

Below is what the user initially asked: ${userPrompt}
${contextSection}
This is their first message: ${firstMessage}

Please help generate a chat title, limited to 20 words maximum.
${languageInstruction}

The title should be:
- Descriptive and specific to the user's request
- Professional and clear
- Maximum 20 words
- Focused on the main topic or question`

    // Use streamObject for reliable structured output
    const result = streamObject({
      model: azure('gpt-4o-mini'),
      schema: titleResponseSchema,
      prompt: systemPrompt,
      temperature: 0.3, // Lower temperature for more consistent titles
      maxOutputTokens: 100, // Short response needed

      onFinish: async result => {
        try {
          const generatedTitle = result.object?.title

          if (!generatedTitle) {
            titleLogger.warn('No title generated', {
              userId,
              metadata: { conversationId },
            })
            return
          }

          titleLogger.info('Title generated successfully', {
            userId,
            metadata: { conversationId, title: generatedTitle },
          })

          // Update conversation title in database
          const updateResult = await updateConversationTitle(
            conversationId,
            generatedTitle
          )

          if (updateResult.success) {
            titleLogger.info('Conversation title updated in database', {
              userId,
              metadata: { conversationId, title: generatedTitle },
            })
          } else {
            titleLogger.error('Failed to update conversation title', {
              userId,
              metadata: { conversationId, error: updateResult.error },
            })
          }

          // Log AI usage
          try {
            await createAIUsage({
              userId,
              entityType: 'aipane',
              entityId: conversationId,
              aiProvider: 'azure_openai',
              modelName: 'gpt-4o-mini',
              usageType: AIUsageType.CHAT,
              inputPrompt: systemPrompt,
              messages: [
                { role: 'user', content: systemPrompt },
                { role: 'assistant', content: generatedTitle },
              ],
              metadata: {
                endpoint: '/api/aipane/generate-title',
                inputTokens: result.usage?.inputTokens || 0,
                outputTokens: result.usage?.outputTokens || 0,
                totalTokens: result.usage?.totalTokens || 0,
                finishReason: 'stop',
                conversationId,
                preferredLanguage,
                generatedTitle,
              },
              config: { temperature: 0.3, maxOutputTokens: 100 },
            })
          } catch (error) {
            titleLogger.error('Failed to log AI usage', error, { userId })
          }
        } catch (error) {
          titleLogger.error('Error in title generation onFinish', error, {
            userId,
            metadata: { conversationId },
          })
        }
      },
    })

    // Return the streaming response
    return result.toTextStreamResponse()
  } catch (error) {
    titleLogger.error('Title generation API error', error)
    return standardErrors.internalError('Error generating title')
  }
}
