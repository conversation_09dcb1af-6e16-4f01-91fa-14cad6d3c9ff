import { streamObject } from 'ai'
import { NextResponse } from 'next/server'
import { azure } from '@ai-sdk/azure'
import { screenSchema } from '@/app/api/screening/utils'
import { auth } from '@/auth'
import { enforceRateLimit } from '@/app/libs/llmRateLimit'
import { getModelConfigForSession } from '@/app/libs/model-config'
import { AIUsageType } from '@prisma/client'
import { createAIUsage } from '@/app/server-actions/log_ai_usage'
import {
  getLanguageName,
  type SupportedLanguageCode,
} from '@/app/constants/languages'
import { z } from 'zod'

export const maxDuration = 60

const requestSchema = z.object({
  userId: z.string().min(1, 'userId is required'),
  description: z.string().min(1, 'description is required'),
  preferredLanguage: z.string().optional(),
})

export async function POST(req: Request) {
  // ────────────────────── AUTH & RATE LIMIT ──────────────────────
  const session = await auth()
  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }
  const rateLimitResp = await enforceRateLimit(session, 'screening_diagnose')
  if (rateLimitResp) return rateLimitResp
  let body: any // Declare body variable in outer scope

  try {
    body = await req.json()

    // Validate request body using Zod schema
    const parseResult = requestSchema.safeParse(body)
    if (!parseResult.success) {
      return NextResponse.json(
        { error: parseResult.error.flatten() },
        { status: 400 }
      )
    }

    const requestData = parseResult.data as {
      userId: string
      description: string
      preferredLanguage?: string
    }

    const { description } = requestData
    const userId = session.user.id
    // Enforce maximum prompt length (10k chars)
    if (description.length > 1_000_000) {
      return NextResponse.json(
        { error: 'Description too long (max 1,000,000 characters)' },
        { status: 400 }
      )
    }

    const preferredLanguage =
      (requestData.preferredLanguage as SupportedLanguageCode) || 'en'

    // Get model configuration based on user's subscription tier
    const modelConfig = await getModelConfigForSession(
      session,
      'screening_diagnose'
    )
    const model_name = modelConfig.model

    const languageName = getLanguageName(preferredLanguage)
    const systemMessage = createSystemMessage(languageName)

    const result = streamObject({
      model: azure(model_name),
      schema: screenSchema,
      system: systemMessage,
      prompt: `User Input: ${description}\n please generate the response in ${languageName}`,
      onFinish: async result => {
        try {
          console.log('✅ [Screening Diagnose] Analysis completed')

          // Log AI usage
          await createAIUsage({
            userId: userId,
            entityType: 'screening',
            entityId: `screening_diagnose_${Date.now()}`, // Generate a unique ID for this screening session
            aiProvider: 'azure_openai',
            modelName: model_name,
            usageType: AIUsageType.SCREENING_PROBLEM_ANALYSIS,
            inputPrompt: description,
            messages: [
              { role: 'system', content: systemMessage },
              { role: 'user', content: `User Input: ${description}` },
              { role: 'assistant', content: JSON.stringify(result.object) },
            ],
            metadata: {
              language: preferredLanguage,
              analysisResult: result.object,
              tokenUsage: result.usage,
            },
            config: {},
          })

          console.log('🎉 [Screening Diagnose] AI usage logged successfully')
        } catch (error) {
          console.error(
            '❌ [Screening Diagnose] Error logging AI usage:',
            error,
            'for user:',
            userId,
            'description length:',
            description.length
          )
          // Don't fail the request if logging fails
        }
      },
    })

    return result.toTextStreamResponse()
  } catch (error) {
    console.error(
      '💥 [Screening Diagnose] API error:',
      error,
      'for request body:',
      JSON.stringify(body || 'undefined', null, 2)
    )
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Helper function to create the system message with language support
function createSystemMessage(languageName: string): string {
  return `
You are an AI assistant designed to screen user inputs for clarity before generating an issue tree. Your goal is to determine if the user's problem statement is clear, specific, and contains sufficient information to proceed.

**Instructions:**

- **Be strict in your analysis.**
- **If the user's main goal or intention is not explicitly stated, you must point it out.**
- Use the attributes as headings in your response.
- For "Specific Entities," interpret any specific people, object, event, location, time, numbers, conditions, etc.
- For "Ambiguities or Unclear Aspects," **always** point out if the main intention is unclear. If the problem statement is clear, state "No obvious ambiguities."
- For "Pass or No Pass," assign **Pass** only if the score is **5**.
- **Do not infer intentions that are not explicitly stated by the user.**

**Examples:**

**User Input:** I am planning to launch a marketing campaign for Acme Corp's new product line.

1. **Intention**: Launch a marketing campaign for Acme Corp's new product line.
2. **Specific Entities**:
   - Acme Corp [a hypothetical company]
   - New product line
3. **Ambiguities or Unclear Aspects**:
   - No obvious ambiguities.
4. **Overall Quality Score**: 5
5. **Pass or No Pass**: true

**User Input:** I sent an email to my boss without a subject line, and now I'm worried.

1. **Intention**: Not clearly stated.
2. **Specific Entities**:
   - Email
   - Boss
3. **Ambiguities or Unclear Aspects**:
   - The user's main goal or intention is not clear.
4. **Overall Quality Score**: 2
5. **Pass or No Pass**: false

Please generate the response in ${languageName}.
`
}
