import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { stripe } from '@/lib/stripe/config'
import { syncStripeState } from '@/lib/stripe/sync'

export const runtime = 'nodejs'
export const dynamic = 'force-dynamic'

export async function POST(req: NextRequest) {
  try {
    const session = await auth()

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { sessionId } = await req.json()

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      )
    }

    // Retrieve the checkout session from Stripe
    const checkoutSession = await stripe.checkout.sessions.retrieve(sessionId)

    if (!checkoutSession) {
      return NextResponse.json({ error: 'Invalid session ID' }, { status: 400 })
    }

    // Verify the session belongs to the current user
    if (checkoutSession.metadata?.user_id !== session.user.id) {
      return NextResponse.json(
        { error: 'Session does not belong to current user' },
        { status: 403 }
      )
    }

    // If this is a subscription checkout, sync the Stripe state
    if (checkoutSession.mode === 'subscription' && checkoutSession.customer) {
      await syncStripeState(checkoutSession.customer as string)
    }

    return NextResponse.json({
      success: true,
      message: 'Subscription processed successfully',
    })
  } catch (error) {
    console.error('Error processing subscription success:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
