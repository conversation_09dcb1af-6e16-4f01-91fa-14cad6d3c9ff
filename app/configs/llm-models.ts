import { SubscriptionTier } from '@prisma/client'

/**
 * Supported LLM model providers
 */
export type ModelProvider = 'azure' | 'openai' | 'anthropic' | 'google'

/**
 * Configuration for a specific LLM model
 */
export type ModelConfig = {
  model: string
  model_provider: ModelProvider
  temperature?: number
  maxOutputTokens?: number
  throttle?: number // throttling in milliseconds for streaming operations
  rateLimitWindowMs?: number // cooldown window per user per route
  rateLimitMaxRequests?: number // max allowed within window
  maxPromptChars?: number // hard cap on input size (characters)
  // Additional provider-specific settings can be added here
}

/**
 * API routes that use LLM models
 * Add new routes here as they are implemented
 */
export type APIRoute =
  | 'dragtree_generate_questions'
  | 'dragtree_research_generate'
  | 'dragtree_generate_similar_questions'
  | 'dragtree_generate_similar_categories'
  | 'screening_diagnose'
  | 'screening_rephrase'
  | 'aipane_generate'
  | 'aipane_chat'

/**
 * Model configuration for all API routes for a specific subscription tier
 */
export type APIRouteConfig = {
  [K in APIRoute]: ModelConfig
}

/**
 * Complete model configuration mapping subscription tiers to API route configs
 */
export type SubscriptionModelConfig = {
  [K in SubscriptionTier]: APIRouteConfig
}

/**
 * Centralized LLM model configuration
 *
 * Strategy:
 * - FREE tier: Cost-effective models for basic functionality
 * - GUEST tier: Premium models for enhanced experience
 *
 * Future tiers can be easily added by extending the SubscriptionTier enum
 * and adding configurations here.
 */
export const LLM_MODEL_CONFIG: SubscriptionModelConfig = {
  VIEWER: {
    // VIEWER tier - minimal configuration (should not be used due to permission restrictions)
    dragtree_generate_questions: {
      model: 'gpt-4.1-mini',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 1000,
      throttle: 500,
    },
    dragtree_research_generate: {
      model: 'gpt-4.1-mini',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 1000,
      throttle: 500,
      rateLimitWindowMs: 5000,
      rateLimitMaxRequests: 1,
    },
    dragtree_generate_similar_questions: {
      model: 'gpt-4.1-mini',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 1000,
      throttle: 500,
    },
    dragtree_generate_similar_categories: {
      model: 'gpt-4.1-mini',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 1000,
      throttle: 500,
    },
    screening_diagnose: {
      model: 'gpt-4.1-mini',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 1000,
      throttle: 500,
    },
    screening_rephrase: {
      model: 'gpt-4.1-mini',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 1000,
      throttle: 500,
    },
    aipane_generate: {
      model: 'gpt-4.1-mini',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 1000,
      throttle: 500,
    },
    aipane_chat: {
      model: 'gpt-4.1-mini',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 1000,
      throttle: 500,
    },
  },
  DUMMY: {
    // DUMMY tier - testing configuration with restricted capabilities
    dragtree_generate_questions: {
      model: 'gpt-4.1-mini',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 5000,
      throttle: 300,
    },
    dragtree_research_generate: {
      model: 'gpt-4.1-mini',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 5000,
      throttle: 300,
      rateLimitWindowMs: 2000,
      rateLimitMaxRequests: 3,
    },
    dragtree_generate_similar_questions: {
      model: 'gpt-4.1-mini',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 5000,
      throttle: 300,
    },
    dragtree_generate_similar_categories: {
      model: 'gpt-4.1-mini',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 5000,
      throttle: 300,
    },
    screening_diagnose: {
      model: 'gpt-4.1-mini',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 5000,
      throttle: 300,
    },
    screening_rephrase: {
      model: 'gpt-4.1-mini',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 5000,
      throttle: 300,
    },
    aipane_generate: {
      model: 'gpt-4.1-mini',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 5000,
      throttle: 300,
    },
    aipane_chat: {
      model: 'gpt-4.1-mini',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 5000,
      throttle: 300,
    },
  },
  FREE: {
    dragtree_generate_questions: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 10000,
      throttle: 200,
    },
    dragtree_research_generate: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 10000,
      throttle: 200,
      rateLimitWindowMs: 1000, // cooldown window per user per route
      rateLimitMaxRequests: 5, // max allowed within window
    },
    dragtree_generate_similar_questions: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 10000,
      throttle: 200,
    },
    dragtree_generate_similar_categories: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 10000,
      throttle: 200,
    },
    screening_diagnose: {
      model: 'gpt-4.1-mini',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 10000,
      throttle: 200,
    },
    screening_rephrase: {
      model: 'gpt-4.1-mini',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 10000,
      throttle: 200,
    },
    aipane_generate: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 10000,
      throttle: 200,
    },
    aipane_chat: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 10000,
      throttle: 200,
    },
  },
  GUEST: {
    dragtree_generate_questions: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 30000,
      throttle: 200,
    },
    dragtree_research_generate: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 30000,
      throttle: 200,
      rateLimitWindowMs: 1000, // cooldown window per user per route
      rateLimitMaxRequests: 5, // max allowed within window
    },
    dragtree_generate_similar_questions: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 30000,
      throttle: 200,
    },
    dragtree_generate_similar_categories: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 30000,
      throttle: 200,
    },
    screening_diagnose: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 30000,
      throttle: 200,
    },
    screening_rephrase: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 30000,
      throttle: 200,
    },
    aipane_generate: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 30000,
      throttle: 200,
    },
    aipane_chat: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 30000,
      throttle: 200,
    },
  },
  PRO: {
    // PRO tier gets same configuration as GUEST for now
    dragtree_generate_questions: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 30000,
      throttle: 200,
    },
    dragtree_research_generate: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 30000,
      throttle: 200,
      rateLimitMaxRequests: 100,
    },
    dragtree_generate_similar_questions: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 30000,
      throttle: 200,
    },
    dragtree_generate_similar_categories: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 30000,
      throttle: 200,
    },

    screening_diagnose: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 30000,
      throttle: 200,
    },
    screening_rephrase: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 30000,
      throttle: 200,
    },
    aipane_generate: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 30000,
      throttle: 200,
    },
    aipane_chat: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 30000,
      throttle: 200,
    },
  },
  ULTRA: {
    // ULTRA tier - future enhanced configuration
    dragtree_generate_questions: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 50000,
      throttle: 100,
    },
    dragtree_research_generate: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 50000,
      throttle: 100,
      rateLimitMaxRequests: 200,
    },
    dragtree_generate_similar_questions: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 50000,
      throttle: 100,
    },
    dragtree_generate_similar_categories: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 50000,
      throttle: 100,
    },

    screening_diagnose: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 50000,
      throttle: 100,
    },
    screening_rephrase: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 50000,
      throttle: 100,
    },
    aipane_generate: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 50000,
      throttle: 100,
    },
    aipane_chat: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 50000,
      throttle: 100,
    },
  },
  BUSINESS: {
    // BUSINESS tier - future enterprise configuration
    dragtree_generate_questions: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 100000,
      throttle: 50,
    },
    dragtree_research_generate: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 100000,
      throttle: 50,
      rateLimitMaxRequests: 500,
    },
    dragtree_generate_similar_questions: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 100000,
      throttle: 50,
    },
    dragtree_generate_similar_categories: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 100000,
      throttle: 50,
    },

    screening_diagnose: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 100000,
      throttle: 50,
    },
    screening_rephrase: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 100000,
      throttle: 50,
    },
    aipane_generate: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 100000,
      throttle: 50,
    },
    aipane_chat: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxOutputTokens: 100000,
      throttle: 50,
    },
  },
}

/**
 * Default fallback configuration if user tier is not found or invalid
 */
export const DEFAULT_MODEL_CONFIG: ModelConfig = {
  model: 'gpt-4.1',
  model_provider: 'azure',
  temperature: 0.7,
  maxOutputTokens: 10000,
}

/**
 * Validates that all subscription tiers have configurations for all API routes
 * This function helps catch configuration errors at build time
 */
export function validateModelConfig(): boolean {
  const requiredTiers: SubscriptionTier[] = [
    'FREE',
    'PRO',
    'GUEST',
    'ULTRA',
    'BUSINESS',
  ]
  const requiredRoutes: APIRoute[] = [
    'dragtree_generate_questions',
    'dragtree_research_generate',
    'dragtree_generate_similar_questions',
    'dragtree_generate_similar_categories',
    'screening_diagnose',
    'screening_rephrase',
    'aipane_generate',
    'aipane_chat',
  ]

  for (const tier of requiredTiers) {
    if (!LLM_MODEL_CONFIG[tier]) {
      console.error(`Missing configuration for subscription tier: ${tier}`)
      return false
    }

    for (const route of requiredRoutes) {
      if (!LLM_MODEL_CONFIG[tier][route]) {
        console.error(`Missing configuration for tier ${tier}, route: ${route}`)
        return false
      }
    }
  }

  return true
}

// Validate configuration on module load
if (!validateModelConfig()) {
  throw new Error('Invalid LLM model configuration detected')
}
