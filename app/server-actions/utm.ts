'use server'

import { cookies } from 'next/headers'
import prisma from '@/app/libs/prismadb'
import { auth } from '@/auth'

/**
 * Persist UTM parameters captured in cookie into User.metadata.
 * Behavior:
 * - On first successful auth with a cookie present:
 *   - If metadata.utm_first is missing, set both utm_first and utm_last from cookie
 *   - If metadata.utm_first exists but utm_last missing, set utm_last
 *   - Else, only update utm_last
 * - Clear the cookie after persisting
 * - If no cookie or not authenticated, no-op
 */

const COOKIE_NAME = '__utm'

type JsonRecord = Record<string, any>

export async function persistUtmFromCookie(): Promise<{
  success: boolean
}> /** adds utm_first and utm_last into user.metadata if cookie is present */ {
  const session = await auth()
  const userId = session?.user?.id
  if (!userId) return { success: false }

  // cookies() returns ReadonlyRequestCookies. No await.
  const cookieStore = cookies() as any
  const raw = cookieStore.get(COOKIE_NAME)?.value
  if (!raw) return { success: false }

  let utm: JsonRecord | null = null
  try {
    utm = JSON.parse(decodeURIComponent(raw)) as JsonRecord
  } catch {
    utm = null
  }

  if (!utm || Object.keys(utm).length === 0) {
    cookieStore.delete(COOKIE_NAME)
    return { success: false }
  }

  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { metadata: true },
  })

  const existing = (user?.metadata as JsonRecord) || {}

  const nextMetadata: JsonRecord = { ...existing }

  if (!existing.utm_first) {
    nextMetadata.utm_first = utm
  }
  // Always update utm_last when we have a cookie
  nextMetadata.utm_last = utm

  await prisma.user.update({
    where: { id: userId },
    data: { metadata: nextMetadata, updated_at: new Date() },
  })

  cookieStore.delete(COOKIE_NAME)
  return { success: true }
}
