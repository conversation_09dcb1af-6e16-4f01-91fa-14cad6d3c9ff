import type { AiMessageRole } from '@prisma/client'

/**
 * Execution step data collected during AI processing
 * This is used to build the complete execution trace with raw provider data
 */
export type ExecutionStepData = {
  stepOrder: number
  type: string // Raw provider type (e.g. "tool-call", "tool-result", "text-delta")
  metadata: { raw: any; [key: string]: any } // { raw: <full step object>, ... }
  parallelKey?: string
  parentStepId?: string
}

/**
 * Attachment data for file uploads
 */
export type AttachmentData = {
  fileName: string
  fileType: string
  fileSize: number
  url: string
}

/**
 * Message data for persistence
 */
export type MessageData = {
  role: AiMessageRole
  content: string
  steps?: ExecutionStepData[]
  attachments?: AttachmentData[]
  metadata?: Record<string, any>
}

/**
 * Complete conversation turn data for atomic persistence
 */
export type ConversationTurnData = {
  conversationId: string
  userMessage: MessageData
  assistantMessage: MessageData
}

/**
 * Conversation creation data
 */
export type ConversationCreateData = {
  userId: string
  title?: string
  contextEntityType?: string
  contextEntityId?: string
  metadata?: Record<string, any>
}

/**
 * Result types for service operations
 */
export type PersistenceResult<T> = {
  success: boolean
  data?: T
  error?: string
}

/**
 * Enhanced error type for better error handling
 */
export type ServiceError = {
  message: string
  code?: string
  details?: Record<string, unknown>
  timestamp: Date
}

/**
 * Enhanced result type with better error handling
 */
export type EnhancedPersistenceResult<T> = {
  success: boolean
  data?: T
  error?: ServiceError
}

/**
 * Conversation with related data
 */
export type ConversationWithMessages = {
  id: string
  userId: string
  title: string | null
  contextEntityType: string | null
  contextEntityId: string | null
  createdAt: Date
  updatedAt: Date
  messages: Array<{
    id: string
    role: AiMessageRole
    content: string
    createdAt: Date
    steps: Array<{
      id: string
      stepOrder: number
      type: string
      metadata: Record<string, any>
      parallelKey: string | null
      parentStepId: string | null
    }>
    attachments: Array<{
      id: string
      fileName: string
      fileType: string
      fileSize: number
      url: string
    }>
  }>
}
