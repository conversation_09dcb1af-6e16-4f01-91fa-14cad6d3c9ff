'use client'

import {
  CheckCircleIcon,
  ChevronDownIcon,
  CircleIcon,
  ClockIcon,
  WrenchIcon,
  XCircleIcon,
} from 'lucide-react'
import type { ComponentProps, ReactNode } from 'react'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import { cn } from '@/lib/utils'
import type { ToolUIPart } from 'ai'

export type ToolProps = ComponentProps<typeof Collapsible>

export const Tool = ({ className, ...props }: ToolProps) => (
  <Collapsible
    className={cn(
      'not-prose mb-1 w-full rounded border border-gray-200/60 bg-gray-50/30 text-xs',
      className
    )}
    {...props}
  />
)

export type ToolHeaderProps = {
  type: ToolUIPart['type']
  state: ToolUIPart['state']
  className?: string
}

const getStatusBadge = (status: ToolUIPart['state']) => {
  const labels = {
    'input-streaming': 'Pending',
    'input-available': 'Running',
    'output-available': 'Done',
    'output-error': 'Error',
  } as const

  const icons = {
    'input-streaming': <CircleIcon className="size-2 text-gray-400" />,
    'input-available': (
      <ClockIcon className="size-2 text-gray-400 animate-pulse" />
    ),
    'output-available': <CheckCircleIcon className="size-2 text-green-500" />,
    'output-error': <XCircleIcon className="size-2 text-red-500" />,
  } as const

  return (
    <div className="flex items-center gap-1 text-xs text-gray-500">
      {icons[status]}
      <span className="text-xs">{labels[status]}</span>
    </div>
  )
}

export const ToolHeader = ({
  className,
  type,
  state,
  ...props
}: ToolHeaderProps) => (
  <CollapsibleTrigger
    className={cn(
      'flex w-full items-center justify-between gap-2 px-2 py-1.5 hover:bg-gray-100/30 text-xs',
      className
    )}
    {...props}
  >
    <div className="flex items-center gap-1.5 min-w-0">
      <WrenchIcon className="size-2.5 text-gray-400 flex-shrink-0" />
      <span className="font-medium text-xs text-gray-600 truncate">{type}</span>
      {getStatusBadge(state)}
    </div>
    <ChevronDownIcon className="size-2.5 text-gray-400 transition-transform group-data-[state=open]:rotate-180 flex-shrink-0" />
  </CollapsibleTrigger>
)

export type ToolContentProps = ComponentProps<typeof CollapsibleContent>

export const ToolContent = ({ className, ...props }: ToolContentProps) => (
  <CollapsibleContent
    className={cn(
      'text-popover-foreground outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:slide-out-to-top-2 data-[state=open]:slide-in-from-top-2',
      className
    )}
    {...props}
  />
)

export type ToolInputProps = ComponentProps<'div'> & {
  input: ToolUIPart['input']
}

export const ToolInput = ({ className, input, ...props }: ToolInputProps) => {
  // Hide parameters by default for cleaner UI - users can expand if needed
  const hasImportantParams = input && Object.keys(input).length > 0
  if (!hasImportantParams) return null

  // Show only essential parameters in a more compact way
  const essentialParams = Object.entries(input || {}).reduce(
    (acc, [key, value]) => {
      // Show key parameters like query, search terms, etc.
      if (
        key.includes('query') ||
        key.includes('search') ||
        key.includes('url') ||
        key.includes('question')
      ) {
        acc[key] = value
      }
      return acc
    },
    {} as Record<string, any>
  )

  if (Object.keys(essentialParams).length === 0) return null

  return (
    <div className={cn('px-2 pb-1.5', className)} {...props}>
      <div className="text-xs text-gray-500 font-mono bg-gray-50/50 px-1.5 py-1 rounded">
        {JSON.stringify(essentialParams, null, 0)}
      </div>
    </div>
  )
}

export type ToolOutputProps = ComponentProps<'div'> & {
  output: ReactNode
  errorText: ToolUIPart['errorText']
}

export const ToolOutput = ({
  className,
  output,
  errorText,
  ...props
}: ToolOutputProps) => {
  if (!(output || errorText)) {
    return null
  }

  // For cleaner UI, show minimal output - hide lengthy search results/snippets
  const shouldHideOutput = () => {
    const outputStr = String(output || '')
    // Hide if output is very long (likely search snippets) or contains common search result patterns
    return (
      outputStr.length > 500 ||
      outputStr.includes('snippet') ||
      outputStr.includes('search_results') ||
      outputStr.includes('web_search')
    )
  }

  if (shouldHideOutput() && !errorText) {
    return (
      <div className={cn('px-2 pb-1.5', className)} {...props}>
        <div className="text-xs text-green-500 flex items-center gap-1">
          <CheckCircleIcon className="size-2" />
          <span>Search completed successfully</span>
        </div>
      </div>
    )
  }

  return (
    <div className={cn('px-2 pb-1.5', className)} {...props}>
      {errorText && (
        <div className="text-xs text-red-500 bg-red-50/50 px-1.5 py-1 rounded">
          {errorText}
        </div>
      )}
      {output && !shouldHideOutput() && (
        <div className="text-xs text-gray-500 bg-gray-50/50 px-1.5 py-1 rounded font-mono">
          {String(output).slice(0, 200)}
          {String(output).length > 200 ? '...' : ''}
        </div>
      )}
    </div>
  )
}
