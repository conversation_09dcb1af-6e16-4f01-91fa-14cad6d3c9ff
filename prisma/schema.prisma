// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema
// format this schema: npx prisma format
// create migrate doc and execute (change db schema): npx prisma migrate dev

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_DATABASE_URL")
}

model Message {
  id              String       @id @default(cuid())
  creator_id      String
  conversation_id String
  content         String
  is_hidden       <PERSON>olean      @default(false)
  created_at      DateTime     @default(now())
  updated_at      DateTime     @updatedAt
  conversation    Conversation @relation(fields: [conversation_id], references: [id], onDelete: Cascade)

  @@index([id], type: Hash)
  @@index([creator_id], type: Hash)
  @@index([conversation_id], type: Hash)
  @@index([created_at])
  @@map("messages")
}

model Feedback {
  id              String       @id @default(cuid())
  creator_id      String
  conversation_id String
  type            FeedbackType @default(ISSUETREE)
  can_contact     <PERSON>olean      @default(false)
  data            Json         @default("{}")
  created_at      DateTime     @default(now())
  updated_at      DateTime     @updatedAt
  conversation    Conversation @relation(fields: [conversation_id], references: [id], onDelete: Cascade)

  @@index([id], type: Hash)
  @@index([creator_id], type: Hash)
  @@index([conversation_id], type: Hash)
  @@index([created_at])
  @@map("feedback")
}

model IssueTree {
  id              String          @id @default(cuid())
  creator_id      String
  conversation_id String
  nodes           String?         @default("")
  edges           String?         @default("")
  raw_markdown    String?         @default("")
  prompt          String?         @default("")
  summary_context String?         @default("")
  status          IssueTreeStatus @default(INITIALIZED)
  config          Json            @default("{}")
  created_at      DateTime        @default(now())
  updated_at      DateTime        @updatedAt
  conversation    Conversation    @relation(fields: [conversation_id], references: [id], onDelete: Cascade)
  notebooks       Notebook[]
  rag_responses   RAGResponse[]
  searches        Search[]
  subtrees        Subtree[]

  @@index([id], type: Hash)
  @@index([creator_id], type: Hash)
  @@index([conversation_id], type: Hash)
  @@index([created_at])
  @@map("issue_trees")
}

model Subtree {
  id                String        @id @default(cuid())
  creator_id        String
  conversation_id   String
  issue_tree_id     String
  nodes             String?       @default("")
  edges             String?       @default("")
  prompt            String?       @default("")
  generation_output String?       @default("")
  status            SubtreeStatus @default(ACTIVE)
  created_at        DateTime      @default(now())
  updated_at        DateTime      @updatedAt
  selected_node_id  String
  conversation      Conversation  @relation(fields: [conversation_id], references: [id], onDelete: Cascade)
  issue_tree        IssueTree     @relation(fields: [issue_tree_id], references: [id], onDelete: Cascade)

  @@index([id], type: Hash)
  @@index([creator_id], type: Hash)
  @@index([conversation_id], type: Hash)
  @@index([issue_tree_id], type: Hash)
  @@index([created_at])
  @@map("subtrees")
}

model Search {
  id               String       @id @default(cuid())
  creator_id       String
  conversation_id  String
  issue_tree_id    String
  selected_node_id String
  search_query     String
  search_result    String
  search_engine    String       @default("BRAVE_SEARCH")
  status           String       @default("ACTIVE")
  type             String       @default("RAG_SEARCH")
  config           Json         @default("{}")
  created_at       DateTime     @default(now())
  updated_at       DateTime     @updatedAt
  conversation     Conversation @relation(fields: [conversation_id], references: [id], onDelete: Cascade)
  issue_tree       IssueTree    @relation(fields: [issue_tree_id], references: [id], onDelete: Cascade)

  @@index([id], type: Hash)
  @@index([creator_id], type: Hash)
  @@index([conversation_id], type: Hash)
  @@index([issue_tree_id], type: Hash)
  @@index([created_at])
  @@map("searches")
}

model RAGResponse {
  id                String       @id @default(cuid())
  creator_id        String
  conversation_id   String
  issue_tree_id     String
  selected_node_id  String
  generation_input  String
  generation_output String
  status            String       @default("ACTIVE")
  type              String       @default("RAG_SEARCH")
  config            Json         @default("{}")
  created_at        DateTime     @default(now())
  updated_at        DateTime     @updatedAt
  conversation      Conversation @relation(fields: [conversation_id], references: [id], onDelete: Cascade)
  issue_tree        IssueTree    @relation(fields: [issue_tree_id], references: [id], onDelete: Cascade)

  @@index([id], type: Hash)
  @@index([creator_id], type: Hash)
  @@index([conversation_id], type: Hash)
  @@index([issue_tree_id], type: Hash)
  @@index([created_at])
  @@map("rag_responses")
}

model Conversation {
  id                  String             @id @default(cuid())
  creator_id          String
  config              Json               @default("{}")
  conversation_type   ConversationType
  is_hidden           Boolean            @default(false)
  created_at          DateTime           @default(now())
  updated_at          DateTime           @updatedAt
  prompt_id           String?
  conversation_status ConversationStatus @default(ACTIVE)
  title               String?            @default("")
  drag_tree_questions String?            @default("")
  conversation_users  ConversationUser[]
  prompt              Prompt?            @relation(fields: [prompt_id], references: [id])
  drafts              Draft[]
  emails              Email[]
  feedback            Feedback[]
  issue_trees         IssueTree[]
  messages            Message[]
  notebooks           Notebook[]
  OpenAIUsage         OpenAIUsage[]
  rag_responses       RAGResponse[]
  searches            Search[]
  subtrees            Subtree[]

  @@index([id], type: Hash)
  @@index([creator_id], type: Hash)
  @@index([created_at])
  @@map("conversations")
}

model Product {
  id            String         @id @default(cuid())
  active        Boolean
  name          String
  description   String?
  image         String?
  metadata      String?
  created_at    DateTime       @default(now())
  updated_at    DateTime       @updatedAt
  prices        Price[]
  subscriptions Subscription[]
  invoices      Invoice[]

  @@index([id], type: Hash)
  @@map("products")
}

model Price {
  id                String              @id @default(cuid())
  product_id        String
  active            Boolean
  description       String?
  unit_amount       BigInt
  currency          String
  type              PricingType
  interval          PricingPlanInterval
  interval_count    Int
  trial_period_days Int?
  metadata          String?
  created_at        DateTime            @default(now())
  updated_at        DateTime            @updatedAt
  product           Product             @relation(fields: [product_id], references: [id])
  subscriptions     Subscription[]
  invoices          Invoice[]

  @@index([id], type: Hash)
  @@map("prices")
}

model Subscription {
  id                   String    @id @default(cuid())
  customer_id          String
  user_id              String
  status               String
  metadata             String?
  price_id             String
  product_id           String
  quantity             Int
  cancel_at_period_end Boolean
  current_period_start DateTime  @default(now())
  current_period_end   DateTime  @default(now())
  ended_at             DateTime?
  cancel_at            DateTime?
  canceled_at          DateTime?
  trial_start          DateTime?
  trial_end            DateTime?
  created_at           DateTime  @default(now())
  updated_at           DateTime  @updatedAt
  price                Price     @relation(fields: [price_id], references: [id])
  product              Product   @relation(fields: [product_id], references: [id])
  user                 User      @relation(fields: [user_id], references: [id])
  invoices             Invoice[]

  @@index([id], type: Hash)
  @@map("subscriptions")
}

// Stripe Invoices snapshot for audit and analytics
model Invoice {
  id                 String   @id @default(cuid())
  customer_id        String
  user_id            String?
  subscription_id    String?
  product_id         String?
  price_id           String?
  status             String
  currency           String
  number             String?
  hosted_invoice_url String?
  invoice_pdf        String?
  subtotal           BigInt?
  total              BigInt
  amount_paid        BigInt?
  amount_due         BigInt?
  attempted          Boolean? @default(false)
  paid               Boolean? @default(false)
  period_start       DateTime?
  period_end         DateTime?
  due_date           DateTime?
  metadata           String?
  created_at         DateTime @default(now())
  updated_at         DateTime @updatedAt

  // Optional relations
  user         User?         @relation(fields: [user_id], references: [id])
  subscription Subscription?  @relation(fields: [subscription_id], references: [id])
  product      Product?      @relation(fields: [product_id], references: [id])
  price        Price?        @relation(fields: [price_id], references: [id])

  @@index([customer_id], type: Hash)
  @@index([user_id], type: Hash)
  @@index([subscription_id], type: Hash)
  @@index([status], type: Hash)
  @@map("invoices")
}

// Stripe PaymentIntents snapshot for audit and analytics
model PaymentIntent {
  id                String   @id @default(cuid())
  customer_id       String?
  user_id           String?
  amount            BigInt
  currency          String
  status            String
  payment_method    String?
  latest_charge     String?
  receipt_url       String?
  description       String?
  metadata          String?
  stripe_created_at DateTime?
  created_at        DateTime @default(now())
  updated_at        DateTime @updatedAt

  // Optional relation
  user User? @relation(fields: [user_id], references: [id])

  @@index([customer_id], type: Hash)
  @@index([user_id], type: Hash)
  @@index([status], type: Hash)
  @@map("payment_intents")
}

model ConversationUser {
  id              String                 @id @default(cuid())
  conversation_id String
  user_id         String
  relationship    EntityUserRelationship
  created_at      DateTime               @default(now())
  updated_at      DateTime               @updatedAt
  conversation    Conversation           @relation(fields: [conversation_id], references: [id], onDelete: Cascade)

  @@index([id], type: Hash)
  @@index([conversation_id], type: Hash)
  @@index([user_id], type: Hash)
  @@index([created_at])
  @@map("conversation_users")
}

model Prompt {
  id            String         @id @default(cuid())
  description   String?
  content       String
  status        PromptStatus   @default(ACTIVE)
  config        Json           @default("{}")
  created_at    DateTime       @default(now())
  updated_at    DateTime       @updatedAt
  creator_id    String?
  type          PromptType?
  conversations Conversation[]
  drafts        Draft[]
  emails        Email[]

  @@index([id], type: Hash)
  @@index([description], type: Hash)
  @@index([created_at])
  @@map("prompts")
}

model Email {
  id              String        @id @default(cuid())
  status          EmailStatus   @default(DELIVERED)
  from            String
  to              String
  subject         String
  text            String
  html            String
  config          Json          @default("{}")
  user_id         String?
  conversation_id String?
  prompt_id       String?
  created_at      DateTime      @default(now())
  updated_at      DateTime      @updatedAt
  conversation    Conversation? @relation(fields: [conversation_id], references: [id])
  prompt          Prompt?       @relation(fields: [prompt_id], references: [id])
  user            User?         @relation(fields: [user_id], references: [id])

  @@index([id], type: Hash)
  @@index([conversation_id], type: Hash)
  @@index([user_id], type: Hash)
  @@index([prompt_id], type: Hash)
  @@map("emails")
}

model Draft {
  id               String        @id @default(cuid())
  status           DraftStatus   @default(ACTIVE)
  type             DraftType     @default(BASE)
  content          String?
  original_content String?
  config           Json          @default("{}")
  user_id          String?
  conversation_id  String?
  prompt_id        String?
  exact_prompt     String?
  created_at       DateTime      @default(now())
  updated_at       DateTime      @updatedAt
  conversation     Conversation? @relation(fields: [conversation_id], references: [id])
  prompt           Prompt?       @relation(fields: [prompt_id], references: [id])
  user             User?         @relation(fields: [user_id], references: [id])

  @@index([id], type: Hash)
  @@index([conversation_id], type: Hash)
  @@index([user_id], type: Hash)
  @@index([prompt_id], type: Hash)
  @@map("drafts")
}

model Notebook {
  id                String         @id @default(cuid())
  user_id           String
  conversation_id   String
  issue_tree_id     String
  status            NotebookStatus @default(INITIALIZED)
  type              NotebookType   @default(BASE)
  title             String         @default("Untitled Notebook")
  content           String         @default("")
  generation_input  String         @default("")
  generation_output String         @default("")
  config            Json?
  remark            String?
  created_at        DateTime       @default(now())
  updated_at        DateTime       @updatedAt
  conversation      Conversation   @relation(fields: [conversation_id], references: [id])
  issue_tree        IssueTree      @relation(fields: [issue_tree_id], references: [id])
  user              User           @relation(fields: [user_id], references: [id])

  @@index([id], type: Hash)
  @@index([conversation_id], type: Hash)
  @@index([user_id], type: Hash)
  @@map("notebooks")
}

model OpenAIUsage {
  id                 String          @id @default(cuid())
  open_ai_usage_type OpenAIUsageType @default(CHAT)
  model_name         String
  input_text         String
  output_text        String
  input_token_est    Int
  output_token_est   Int
  config             Json            @default("{}")
  user_id            String?
  conversation_id    String?
  created_at         DateTime        @default(now())
  updated_at         DateTime        @updatedAt
  conversation       Conversation?   @relation(fields: [conversation_id], references: [id])
  user               User?           @relation(fields: [user_id], references: [id])

  @@index([id], type: Hash)
  @@index([conversation_id], type: Hash)
  @@index([user_id], type: Hash)
  @@index([model_name], type: Hash)
  @@index([created_at])
  @@map("openai_usage")
}

model Account {
  id                String   @id @default(cuid())
  userId            String   @map("user_id")
  type              String
  provider          String
  providerAccountId String   @map("provider_account_id")
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  created_at        DateTime @default(now())
  updated_at        DateTime @updatedAt
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId], type: Hash)
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique @map("session_token")
  userId       String
  expires      DateTime
  created_at   DateTime @default(now())
  updated_at   DateTime @updatedAt
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId], type: Hash)
  @@map("sessions")
}

model User {
  id                       String               @id @default(cuid())
  name                     String?
  email                    String?              @unique
  emailVerified            DateTime?            @map("email_verified")
  image                    String?
  created_at               DateTime             @default(now())
  updated_at               DateTime             @updatedAt
  status                   UserStatus           @default(ACTIVE)
  subscription_end_date    DateTime?
  subscription_tier        SubscriptionTier     @default(FREE)
  subscription_customer_id    String?
  subscription_id             String?
  subscription_service        SubscriptionService?
  subscription_cancel_pending Boolean              @default(false)
  metadata                    Json                 @default("{}")
  accounts                 Account[]
  ai_generations           AIGeneration[]
  ai_usage                 AIUsage[]
  aiConversations          AiConversation[]
  drafts                   Draft[]
  drag_trees               DragTree[]
  emails                   Email[]
  notebooks                Notebook[]
  OpenAIUsage              OpenAIUsage[]
  sessions                 Session[]
  subscriptions            Subscription[]
  invoices                 Invoice[]
  payment_intents         PaymentIntent[]
  UserFeedback             UserFeedback[]

  @@index([id], type: Hash)
  @@index([email], type: Hash)
  @@map("users")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  @@unique([identifier, token])
  @@index([token], type: Hash)
  @@map("verification_token")
}

model DragTree {
  id                     String                @id
  user_id                String
  title                  String?
  status                 DragTreeStatus        @default(INITIALIZED)
  tree_structure         Json?
  tree_structure_version Int?                  @default(1)
  user_prompt            String?
  raw_markdown           String?
  metadata               Json                  @default("{}")
  created_at             DateTime              @default(now())
  updated_at             DateTime              @updatedAt
  preferred_language     String?               @default("en")
  user                   User                  @relation(fields: [user_id], references: [id], onDelete: Cascade)
  nodes                  DragTreeNode[]
  content_items          DragTreeNodeContent[]
  // Reverse relation for AI generations associated with this drag tree
  aiGenerations          AIGeneration[]        @relation("DragTreeGenerations")

  @@index([id], type: Hash)
  @@index([user_id], type: Hash)
  @@index([status], type: Hash)
  @@index([created_at])
  @@map("drag_tree")
}

model DragTreeNode {
  id                 String                @id
  drag_tree_id       String
  node_type          DragTreeNodeType
  label              String
  status             DragTreeNodeStatus    @default(ACTIVE)
  is_interested_in   Boolean               @default(false)
  ui_state           Json                  @default("{}")
  metadata           Json                  @default("{}")
  version            String                @default("v1")
  content_summary    Json                  @default("{}")
  content_updated_at DateTime?
  created_at         DateTime              @default(now())
  updated_at         DateTime              @updatedAt
  drag_tree          DragTree              @relation(fields: [drag_tree_id], references: [id], onDelete: Cascade)
  content_items      DragTreeNodeContent[]

  @@index([id], type: Hash)
  @@index([drag_tree_id], type: Hash)
  @@index([created_at])
  @@map("drag_tree_node")
}

model DragTreeNodeContent {
  id                  String                    @id @default(cuid())
  drag_tree_id        String
  drag_tree_node_id   String
  status              DragTreeNodeContentStatus @default(INITIALIZED)
  content_version     String?                   @default("v1")
  content_text        String?                   @default("")
  content_metadata    Json?                     @default("{}")
  messages            Json?                     @default("[]")
  generation_metadata Json?                     @default("{}")
  created_at          DateTime                  @default(now())
  updated_at          DateTime                  @updatedAt
  content_type        DragTreeNodeContentType
  drag_tree           DragTree                  @relation(fields: [drag_tree_id], references: [id], onDelete: Cascade)
  drag_tree_node      DragTreeNode              @relation(fields: [drag_tree_node_id], references: [id], onDelete: Cascade)

  @@index([id], type: Hash)
  @@index([drag_tree_id], type: Hash)
  @@index([drag_tree_node_id], type: Hash)
  @@index([status], type: Hash)
  @@index([created_at])
  @@map("drag_tree_node_content")
}

// This is similar to model Notebook, but it is recreated because of more general design ofentity_type and entity_id
model AIGeneration {
  id                String             @id @default(cuid())
  user_id           String
  entity_type       String
  entity_id         String
  status            AIGenerationStatus @default(INITIALIZED)
  title             String?
  content           String             @default("")
  generation_input  String             @default("")
  generation_output String             @default("")
  // Related to the generation process
  config            Json               @default("{}")
  // Placeholder for potential future use, eg: different entity_type may have different logic requiring data persistance
  metadata          Json               @default("{}")
  // Optimistic locking to prevent race conditions
  version           Int                @default(1)
  created_at        DateTime           @default(now())
  updated_at        DateTime           @updatedAt
  user              User               @relation(fields: [user_id], references: [id], onDelete: Cascade)
  // Optional relation for drag_tree_v1 entities to enable atomic ownership validation
  dragTree          DragTree?          @relation("DragTreeGenerations", fields: [entity_id], references: [id])

  @@index([user_id, created_at])
  @@index([entity_type, entity_id, status])
  @@map("ai_generations")
}

model AIUsage {
  id           String      @id @default(cuid())
  user_id      String
  entity_type  String
  entity_id    String
  ai_provider  String
  model_name   String
  usage_type   AIUsageType
  input_prompt String
  messages     Json        @default("[]")
  metadata     Json        @default("{}")
  config       Json        @default("{}")
  created_at   DateTime    @default(now())
  updated_at   DateTime    @updatedAt
  user         User        @relation(fields: [user_id], references: [id])

  @@index([id], type: Hash)
  @@index([user_id], type: Hash)
  @@index([entity_type], type: Hash)
  @@index([entity_id], type: Hash)
  @@index([ai_provider], type: Hash)
  @@index([model_name], type: Hash)
  @@index([usage_type], type: Hash)
  @@index([created_at])
  @@map("ai_usage")
}

// Generic, extensible user feedback table decoupled from any specific entity schema
model UserFeedback {
  id            String           @id @default(cuid())
  user_id       String
  // Where this feedback originates from (e.g. DRAGTREE_FLOATING_BUTTON)
  feedback_type UserFeedbackType
  // Polymorphic reference to any entity in the product (e.g. DRAGTREE, NOTEBOOK, etc.)
  entity_type   String
  entity_id     String
  // Whether user consents to follow-up contact
  can_contact   Boolean          @default(false)
  // Optional freeform text the user wrote
  feedback_text String?          @default("")
  // Rich, structured metadata to capture context and future extensibility
  // Example: { feedback_reason: 'bug' | 'suggestion', survey_scores: { smoothness: 4, quality: 5, effort: 3 }, engagement_level: 'connect' | 'open' | 'listening', ui_state: {...} }
  metadata      Json             @default("{}")
  created_at    DateTime         @default(now())
  updated_at    DateTime         @updatedAt
  user          User             @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id], type: Hash)
  @@index([feedback_type], type: Hash)
  @@index([entity_type, entity_id])
  @@index([created_at])
  @@map("user_feedback")
}

enum AIGenerationStatus {
  INITIALIZED
  GENERATING // actively streaming tokens
  ACTIVE // completed successfully
  INACTIVE // failed or archived
}

enum ConversationType {
  SELF_CLARIFY
  ISSUE_TREE
}

enum EntityUserRelationship {
  OWNER
  EDITOR
  READER
}

enum UserStatus {
  ACTIVE
  INACTIVE
}

enum PromptStatus {
  ACTIVE
  INACTIVE
}

enum PromptType {
  BASE
  POST_CONVERSATION
}

enum IssueTreeStatus {
  INITIALIZED
  ACTIVE
  COMPLETED
  GENERATING
}

enum SubtreeStatus {
  ACTIVE
  MERGED
  INACTIVE
}

enum FeedbackType {
  ISSUETREE
}

enum ConversationStatus {
  ACTIVE
  COMPLETED
  INACTIVE
  EXAMPLE
  FEEDBACK
  INITIALIZED
  DRAGTREE_GENERATED
}

enum EmailStatus {
  DELIVERED
  FAILED
}

enum DraftStatus {
  ACTIVE
  INACTIVE
}

enum DraftType {
  BASE
}

enum NotebookStatus {
  INITIALIZED
  ACTIVE
  INACTIVE
}

enum NotebookType {
  BASE
}

enum OpenAIUsageType {
  EMAIL_ANALYSIS
  CHAT
  DRAFTED_DOCUMENT
  EDITOR_GENERATE
  ISSUE_TREE_GENERATE_QUESTIONS
  ISSUE_TREE_GENERATE_SUMMARY
  REPHRASE_PROBLEM
  SUBTREE_GENERATE_QUESTIONS
  RAG_QUERY_REPHRASER
  RAG_GENERATE_RESPONSE
  NOTEBOOK_GENERATE
  SCREEN_PROBLEM
  DRAGTREE_GENERATE_QUESTIONS
  DRAGTREE_GENERATE_SIMILAR_QUESTIONS
  DRAGTREE_GENERATE_SIMILAR_CATEGORIES
  DRAGTREE_NODE_ANSWER
}

enum DragTreeStatus {
  INITIALIZED
  GENERATING
  ACTIVE
  INACTIVE
}

enum DragTreeNodeType {
  CATEGORY
  QUESTION
}

enum DragTreeNodeStatus {
  ACTIVE
  INACTIVE
}

enum DragTreeNodeContentStatus {
  INITIALIZED
  PROCESSING
  ACTIVE
  INACTIVE
}

enum DragTreeNodeContentType {
  QUICK_RESEARCH
}

// Feedback sources/types for the generic user feedback table
enum UserFeedbackType {
  DRAGTREE_FLOATING_BUTTON
}

enum AIUsageType {
  SCREENING_PROBLEM_ANALYSIS
  SCREENING_REPHRASE
  GENERATE_QUESTION
  NODE_QUICK_RESEARCH
  CHAT
}

enum PricingType {
  one_time
  recurring
}

enum PricingPlanInterval {
  day
  week
  month
  year
}

enum SubscriptionTier {
  FREE      // default for all sign-ups
  PRO       // paying customers
  GUEST     // friends & testers → same runtime permissions as PRO
  VIEWER    // read-only eyeballing; no capabilities
  DUMMY     // dummy tier for testing
  ULTRA     // reserved (higher usage limits)
  BUSINESS  // reserved (multi-seat, SLA)
}

enum SubscriptionService {
  STRIPE
}

// -------------------------------------------------------------
// NEW EXTENSIBLE CHAT SCHEMA (AI-PREFIXED TABLES)
// -------------------------------------------------------------

// Defines the role for a given AI message
enum AiMessageRole {
  USER
  ASSISTANT
  SYSTEM
}

// Defines the type of AI execution step for full traceability
enum AiStepType {
  THOUGHT
  TOOL_CALL
  TOOL_RESULT
  REASONING_SUMMARY
  SUB_AGENT_INVOCATION
  TITLE
  CITATIONS
  STATUS_UPDATE
}

// Main conversation container with polymorphic context support
model AiConversation {
  id                String             @id @default(cuid()) @map("id") // thread_<cuid>
  userId            String             @map("user_id")
  title             String?            @map("title")
  contextEntityType String?            @map("context_entity_type")
  contextEntityId   String?            @map("context_entity_id")
  metadata          Json?              @map("metadata") // { contextIds: string[] }
  status            AIGenerationStatus @default(ACTIVE) @map("status") // Consistent with AIGeneration

  // --- Timestamps ---
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // --- Relations ---
  user     User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  messages AiMessage[]

  @@index([userId])
  @@index([contextEntityType, contextEntityId])
  @@index([userId, updatedAt]) // For user conversation lists ordered by recent activity
  @@index([createdAt]) // For temporal queries
  @@index([status]) // For status-based filtering
  @@index([userId, status]) // For user + status queries
  @@map("ai_conversations")
}

// User-facing message content (clean, final output)
model AiMessage {
  id             String        @id @default(cuid()) // msg_<cuid>
  conversationId String        @map("conversation_id")
  role           AiMessageRole
  content        String        @db.Text
  metadata       Json?         @map("metadata") // { runCompleted: boolean, tokenUsage: ... }
  ui_message     Json?         @map("ui_message") // canonical UIMessage blob

  // --- Timestamps ---
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // --- Relations ---
  conversation AiConversation    @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  steps        AiExecutionStep[]
  attachments  AiAttachment[]

  @@index([conversationId])
  @@index([conversationId, createdAt]) // For paginated message queries
  @@index([role, createdAt]) // For role-based message filtering
  @@index([createdAt]) // For temporal message queries
  @@map("ai_messages")
}

// Detailed execution tracing for AI reasoning and tool use
model AiExecutionStep {
  id        String @id @default(cuid()) // step_<cuid>
  messageId String @map("message_id")
  stepOrder Int    @map("step_order") // 0,1,2 …
  type      String // Raw provider type (e.g. "tool-call", "tool-result", "text-delta")
  metadata  Json // { raw: <full step object>, ... }

  // --- Advanced coordination (optional) ---
  parallelKey  String? @map("parallel_key") // e.g. toolBatch-42
  parentStepId String? @map("parent_step_id")

  // --- Timestamps ---
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // --- Relations ---
  message AiMessage @relation(fields: [messageId], references: [id], onDelete: Cascade)

  @@unique([messageId, stepOrder]) // Prevent race conditions
  @@index([messageId, stepOrder])
  @@index([type, createdAt]) // For querying steps by type across messages
  @@index([parallelKey]) // For parallel execution queries
  @@index([parentStepId]) // For hierarchical step queries
  @@map("ai_execution_steps")
}

// File/attachment support for messages
model AiAttachment {
  id        String @id @default(cuid()) // file_<cuid>
  messageId String @map("message_id")
  fileName  String @map("file_name")
  fileType  String @map("file_type") // image/png, application/pdf …
  fileSize  Int    @map("file_size")
  url       String @db.Text

  // --- Timestamps ---
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // --- Relations ---
  message AiMessage @relation(fields: [messageId], references: [id], onDelete: Cascade)

  @@index([messageId])
  @@index([fileType]) // For querying attachments by type
  @@index([messageId, createdAt]) // For attachment ordering within messages
  @@map("ai_attachments")
}
