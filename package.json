{"name": "next13-clarify", "version": "0.1.0", "private": true, "scripts": {"dev": "NEXT_DIST_DIR=.next-dev next dev", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit", "test": "jest --runInBand --silent=false", "test:watch": "jest --watch --verbose", "test:coverage": "jest --coverage --verbose", "test:verbose": "jest --verbose", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:all": "npm run test && npm run test:e2e", "postinstall": "prisma generate", "vercel-build": "prisma generate && prisma migrate deploy && next build", "prepare": "husky"}, "dependencies": {"@ai-sdk/azure": "^2.0.0", "@ai-sdk/openai": "2.0.8", "@ai-sdk/react": "^2.0.0", "@auth/prisma-adapter": "^1.0.0", "@headlessui/react": "^2.2.4", "@nem035/gpt-3-encoder": "^1.1.7", "@prisma/client": "^6.9.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/react-use-controllable-state": "^1.2.2", "@sendgrid/mail": "^7.7.0", "@smastrom/react-rating": "^1.3.2", "@stripe/stripe-js": "^3.5.0", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/typography": "^0.5.9", "@tiptap/core": "^2.22.3", "@tiptap/extension-code-block-lowlight": "^2.22.3", "@tiptap/extension-color": "^2.22.3", "@tiptap/extension-highlight": "^2.22.3", "@tiptap/extension-horizontal-rule": "^2.22.3", "@tiptap/extension-link": "^2.22.3", "@tiptap/extension-placeholder": "^2.22.3", "@tiptap/extension-table": "^2.23.1", "@tiptap/extension-table-cell": "^2.23.1", "@tiptap/extension-table-header": "^2.23.1", "@tiptap/extension-table-row": "^2.23.1", "@tiptap/extension-task-item": "^2.22.3", "@tiptap/extension-task-list": "^2.22.3", "@tiptap/extension-text-style": "^2.22.3", "@tiptap/extension-underline": "^2.22.3", "@tiptap/html": "^2.22.3", "@tiptap/pm": "^2.22.3", "@tiptap/react": "^2.22.3", "@tiptap/starter-kit": "^2.22.3", "@tiptap/suggestion": "^2.22.3", "@types/crypto-js": "^4.2.2", "@types/marked": "^5.0.0", "@types/node": "20.3.1", "@types/nodemailer": "^6.4.17", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-syntax-highlighter": "^15.5.13", "@upstash/ratelimit": "^0.4.3", "@vercel/analytics": "^1.0.1", "@vercel/blob": "^0.10.0", "@vercel/kv": "^0.2.2", "ai": "^5.0.9", "autoprefixer": "10.4.14", "axios": "^1.4.0", "class-variance-authority": "^0.6.0", "clsx": "^1.2.1", "cmdk": "^1.0.0", "crypto-js": "^4.2.0", "dagre": "^0.8.5", "date-fns": "^2.30.0", "elkjs": "^0.10.0", "embla-carousel-react": "^8.6.0", "eslint": "^8.57.0", "eslint-config-next": "^15.3.4", "framer-motion": "^11.2.14", "gpt-tokenizer": "^3.0.1", "harden-react-markdown": "^1.0.4", "highlight.js": "^11.9.0", "html-to-image": "^1.11.13", "http-status-codes": "^2.3.0", "immer": "^10.1.1", "isomorphic-dompurify": "^2.16.0", "jsonwebtoken": "^9.0.1", "katex": "^0.16.22", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "lodash.isequal": "^4.5.0", "lowlight": "^2.9.0", "lucide-react": "^0.523.0", "marked": "^14.1.2", "mermaid": "^10.4.0", "mixpanel-browser": "^2.47.0", "next": "^15.3.4", "next-auth": "5.0.0-beta.29", "nodemailer": "^6.10.1", "openai": "^4.38.5", "openai-edge": "^1.1.0", "p-limit": "^6.2.0", "postcss": "8.4.24", "posthog-js": "^1.255.1", "posthog-node": "^5.1.1", "react": "^19.1.0", "react-confetti": "^6.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.45.0", "react-hot-toast": "^2.4.1", "react-icons": "^4.12.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.3", "react-syntax-highlighter": "^15.6.1", "react-textarea-autosize": "^8.4.1", "react-virtualized-auto-sizer": "^1.0.7", "react-window": "^1.8.8", "react-youtube": "^10.1.0", "reactflow": "^11.8.3", "rehype-katex": "^7.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "remark-parse": "^11.0.0", "resend": "^6.0.1", "sass": "^1.64.2", "stripe": "^15.10.0", "tailwind-merge": "^1.13.2", "tailwindcss": "3.3.2", "tailwindcss-animate": "^1.0.6", "tiptap-markdown": "^0.8.10", "unified": "^11.0.5", "use-debounce": "^9.0.4", "use-stick-to-bottom": "^1.1.1", "uuid": "^11.1.0", "zod": "^3.23.8"}, "devDependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@playwright/test": "^1.53.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/dagre": "^0.7.52", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.18", "@types/lodash.debounce": "^4.0.9", "@types/lodash.isequal": "^4.5.8", "@types/mixpanel-browser": "^2.47.4", "@types/react-window": "^1.8.7", "@types/testing-library__jest-dom": "^5.14.9", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "@typescript-eslint/typescript-estree": "^8.39.0", "husky": "^9.1.7", "jest": "^30.0.0", "jest-environment-jsdom": "^30.0.0", "jest-junit": "^16.0.0", "lint-staged": "^16.1.0", "postcss-nesting": "^12.1.5", "prettier": "^3.5.3", "prisma": "^6.9.0", "typescript": "^5.6.2", "typescript-eslint": "^8.34.0"}, "lint-staged": {"*.{ts,tsx,js,jsx,css,md,json}": ["prettier --write"]}, "volta": {"node": "20.19.4"}}