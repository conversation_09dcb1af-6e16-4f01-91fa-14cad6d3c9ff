import NextAuth, { DefaultSession } from 'next-auth'
import GithubProvider from 'next-auth/providers/github'
import GoogleProvider from 'next-auth/providers/google'
import EmailProvider from 'next-auth/providers/email'
import CredentialsProvider from 'next-auth/providers/credentials'
import { PrismaAdapter } from '@auth/prisma-adapter'
import { Resend } from 'resend'

import prisma from '@/app/libs/prismadb'
import { UserStatus, SubscriptionTier } from '@prisma/client'

// Extend the built-in session types
declare module 'next-auth' {
  interface Session {
    user: {
      id: string
      status: UserStatus
      subscription_tier: SubscriptionTier
      subscription: {
        tier: SubscriptionTier
        expiry: string | null
        cancelPending: boolean
      }
    } & DefaultSession['user']
  }
}

declare module 'next-auth' {
  interface JWT {
    id: string
    status: UserStatus
    subscription_tier: SubscriptionTier
    tierExpiry: string | null
    cancelPending: boolean
  }
}

// Initialize Resend client for email provider
const resend = new Resend(process.env.RESEND_API_KEY)

// Determine a safe "from" address. Use Resend's onboarding sender in dev by default
const getFromAddress = (): string => {
  // Prefer Resend onboarding sender in non-production to avoid domain verification issues
  if (process.env.NODE_ENV !== 'production') return '<EMAIL>'
  const envFrom = process.env.EMAIL_FROM?.trim()
  if (envFrom) return envFrom
  return '<EMAIL>'
}

const baseProviders = [
  GithubProvider({
    clientId: process.env.GITHUB_ID as string,
    clientSecret: process.env.GITHUB_SECRET as string,
  }),
  GoogleProvider({
    clientId: process.env.GOOGLE_CLIENT_ID as string,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
  }),
  EmailProvider({
    // Provide SMTP server to satisfy Auth.js nodemailer checks, even though we
    // override sending with Resend API in sendVerificationRequest.
    server: {
      host: 'smtp.resend.com',
      port: 587,
      auth: {
        user: 'resend',
        pass: process.env.RESEND_API_KEY,
      },
    },
    from: getFromAddress(),
    sendVerificationRequest: async ({ identifier: email, url, provider }) => {
      try {
        const fromAddress = (provider.from as string) || getFromAddress()
        const response = await resend.emails.send({
          from: fromAddress,
          to: email,
          subject: 'Sign in to Clarify AI',
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
              <div style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: #1f2937; margin: 0;">Clarify AI</h1>
                <p style="color: #6b7280; margin: 10px 0 0 0;">Welcome to the future of clear communication</p>
              </div>

              <div style="background: #f9fafb; border-radius: 8px; padding: 30px; margin: 20px 0;">
                <h2 style="color: #1f2937; margin: 0 0 20px 0;">Sign in to your account</h2>
                <p style="color: #4b5563; margin: 0 0 25px 0; line-height: 1.6;">
                  Click the button below to securely sign in to your Clarify AI account. This link will expire in 24 hours.
                </p>

                <div style="text-align: center; margin: 30px 0;">
                  <a href="${url}"
                     style="background: #3b82f6; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: 600; display: inline-block;">
                    Sign in to Clarify AI
                  </a>
                </div>

                <p style="color: #6b7280; font-size: 14px; margin: 25px 0 0 0; line-height: 1.5;">
                  If you didn't request this email, you can safely ignore it. The link will expire automatically.
                </p>
              </div>

              <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                <p style="color: #9ca3af; font-size: 12px; margin: 0;">
                  © 2024 Clarify AI. All rights reserved.
                </p>
              </div>
            </div>
          `,
          text: `Sign in to your Clarify AI account:\n\n${url}\n\nIf you didn't request this email, you can ignore it.`,
        })

        // Basic sanity logging to help diagnose delivery in dev
        if (process.env.NODE_ENV !== 'production') {
          console.log('[Auth] Resend email response', response)
        }
      } catch (error) {
        console.error('Failed to send magic link email:', error)
        throw new Error('Failed to send verification email')
      }
    },
  }),
] as const

const allProviders: any[] = [...baseProviders]

// Add credentials provider only for non-production environments
if (process.env.NODE_ENV !== 'production') {
  allProviders.push(
    CredentialsProvider({
      name: 'E2E Test Credentials',
      credentials: {
        email: {
          label: 'Email',
          type: 'text',
          placeholder: '<EMAIL>',
        },
      },
      async authorize(credentials) {
        if (!credentials?.email) {
          throw new Error('Email is required for E2E login.')
        }

        const user = await prisma.user.findUnique({
          where: { email: credentials.email as string },
        })

        if (user) {
          // Return user object to create a session
          return user
        } else {
          // If you want to auto-create a user, you can do it here
          return null
        }
      },
    })
  )
}

export const { handlers, auth, signIn, signOut } = NextAuth({
  adapter: PrismaAdapter(prisma),
  providers: allProviders,
  debug: process.env.NODE_ENV === 'development',
  session: {
    strategy: 'jwt',
    maxAge: 7 * 24 * 60 * 60, // 7 days in seconds
  },
  pages: {
    signIn: '/',
    // After successful sign-in, default to screening unless a callbackUrl is specified.
    // Note: Client can still override via signIn(provider, { callbackUrl: '/screening' })
  },
  callbacks: {
    signIn: async ({ user }) => {
      // Check if user exists and is active
      const dbUser = await prisma.user.findUnique({
        where: { email: user.email as string },
        select: { status: true },
      })

      // Allow sign-in for new users or active existing users
      return !dbUser || dbUser.status === UserStatus.ACTIVE
    },

    jwt: async ({ token, user, trigger, session }) => {
      if (user) {
        // Fetch user data from database
        const dbUser = await prisma.user.findUnique({
          where: { email: user.email as string },
          select: {
            id: true,
            status: true,
            subscription_tier: true,
            subscription_end_date: true,
            subscription_cancel_pending: true,
          },
        })

        // Add custom claims to the token
        if (dbUser) {
          token.id = dbUser.id
          token.status = dbUser.status
          token.subscription_tier = dbUser.subscription_tier
          token.tierExpiry = dbUser.subscription_end_date?.toISOString() || null
          token.cancelPending = dbUser.subscription_cancel_pending || false
        }
      }

      // Refresh subscription data on session update
      if (trigger === 'update' && token.id) {
        const dbUser = await prisma.user.findUnique({
          where: { id: token.id as string },
          select: {
            subscription_tier: true,
            subscription_end_date: true,
            subscription_cancel_pending: true,
          },
        })

        if (dbUser) {
          token.subscription_tier = dbUser.subscription_tier
          token.tierExpiry = dbUser.subscription_end_date?.toISOString() || null
          token.cancelPending = dbUser.subscription_cancel_pending || false
        }
      }

      // Handle manual session.update() calls – refresh subscription tier
      if (trigger === 'update') {
        if (
          session?.subscription_tier &&
          session.subscription_tier !== token.subscription_tier
        ) {
          token.subscription_tier =
            session.subscription_tier as SubscriptionTier
        } else if (token?.id) {
          // fetch latest tier if not provided
          try {
            const refreshedUser = await prisma.user.findUnique({
              where: { id: token.id as string },
              select: { subscription_tier: true },
            })
            if (refreshedUser?.subscription_tier) {
              token.subscription_tier = refreshedUser.subscription_tier
            }
          } catch (err) {
            console.error(
              'Failed to refresh subscription tier during JWT update',
              err
            )
          }
        }
      }

      return token
    },

    session: ({ session, token }) => {
      // Add custom session data
      if (session.user) {
        session.user.id = token.id as string
        session.user.status = token.status as UserStatus
        session.user.subscription_tier =
          token.subscription_tier as SubscriptionTier
        session.user.subscription = {
          tier: token.subscription_tier as SubscriptionTier,
          expiry: token.tierExpiry as string | null,
          cancelPending: token.cancelPending as boolean,
        }
      }
      return session
    },
  },
  secret: process.env.NEXTAUTH_SECRET || '',
})
